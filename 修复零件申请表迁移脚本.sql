-- =============================================
-- 修复零件申请表迁移脚本
-- 创建日期: 2025-01-07
-- 版本: 2.1.0
-- 描述: 修复字段名错误和重复索引问题
-- =============================================

USE [EquipmentManagement]
GO

PRINT '开始修复零件申请表迁移问题...'
PRINT '========================================'

-- =============================================
-- 第一步：删除有问题的视图
-- =============================================

PRINT '1. 删除有问题的视图...'

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrderPartRequestDetails]
    PRINT '✓ 删除旧的V_RepairOrderPartRequestDetails视图'
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrderPartSummary]
    PRINT '✓ 删除旧的V_RepairOrderPartSummary视图'
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrdersWithParts]'))
BEGIN
    DROP VIEW [dbo].[V_RepairOrdersWithParts]
    PRINT '✓ 删除旧的V_RepairOrdersWithParts视图'
END

-- =============================================
-- 第二步：删除重复的索引
-- =============================================

PRINT '2. 删除重复的索引...'

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalPartNumber')
BEGIN
    DROP INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests]
    PRINT '✓ 删除重复的外部零件编号索引'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalRequisitionDetailId')
BEGIN
    DROP INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests]
    PRINT '✓ 删除重复的外部领用单明细ID索引'
END

-- =============================================
-- 第三步：重新创建索引
-- =============================================

PRINT '3. 重新创建索引...'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalPartNumber')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests] ([ExternalPartNumber])
    WHERE [ExternalPartNumber] IS NOT NULL
    PRINT '✓ 外部零件编号索引创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalRequisitionDetailId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests] ([ExternalRequisitionDetailId])
    WHERE [ExternalRequisitionDetailId] IS NOT NULL
    PRINT '✓ 外部领用单明细ID索引创建成功'
END

-- =============================================
-- 第四步：重新创建正确的视图
-- =============================================

PRINT '4. 重新创建正确的视图...'

-- 创建零件申请详细视图（修复字段名）
CREATE VIEW [dbo].[V_RepairOrderPartRequestDetails]
AS
SELECT 
    pr.Id,
    pr.RepairOrderId,
    ro.OrderNumber as RepairOrderNumber,
    ro.FaultDescription as RepairOrderDescription,  -- 使用FaultDescription而不是Title
    pr.PartName,
    pr.Specification,
    pr.RequestedQuantity,
    pr.Unit,
    pr.Reason,
    pr.Remark,
    pr.Status,
    CASE pr.Status
        WHEN 1 THEN N'申请中'
        WHEN 2 THEN N'已领用'
        WHEN 3 THEN N'已安装'
        WHEN 4 THEN N'已取消'
        ELSE N'未知'
    END AS StatusName,
    pr.RequestedBy,
    ru.DisplayName as RequestedByName,  -- 使用DisplayName而不是Name
    pr.RequestedAt,
    pr.ApprovedBy,
    au.DisplayName as ApprovedByName,   -- 使用DisplayName而不是Name
    pr.ApprovedAt,
    pr.IssuedBy,
    iu.DisplayName as IssuedByName,     -- 使用DisplayName而不是Name
    pr.IssuedAt,
    pr.InstalledBy,
    inu.DisplayName as InstalledByName, -- 使用DisplayName而不是Name
    pr.InstalledAt,
    pr.ExternalPartNumber,
    pr.ExternalRequisitionDetailId,
    pr.ActualQuantity,
    pr.ActualPartName,
    pr.ActualSpecification,
    pr.UnitPrice,
    pr.TotalCost,
    pr.WarehouseOrderNumber,
    pr.CreatedAt,
    pr.UpdatedAt
FROM [dbo].[RepairOrderPartRequests] pr
    INNER JOIN [dbo].[RepairOrders] ro ON pr.RepairOrderId = ro.Id
    LEFT JOIN [dbo].[Users] ru ON pr.RequestedBy = ru.Id
    LEFT JOIN [dbo].[Users] au ON pr.ApprovedBy = au.Id
    LEFT JOIN [dbo].[Users] iu ON pr.IssuedBy = iu.Id
    LEFT JOIN [dbo].[Users] inu ON pr.InstalledBy = inu.Id
GO

PRINT '✓ 零件申请详细视图创建成功'

-- 创建维修单零件统计视图（修复字段名）
CREATE VIEW [dbo].[V_RepairOrderPartSummary]
AS
SELECT 
    ro.Id as RepairOrderId,
    ro.OrderNumber,
    ro.FaultDescription as Description,  -- 使用FaultDescription而不是Title
    COUNT(pr.Id) as TotalPartRequests,
    SUM(CASE WHEN pr.Status = 1 THEN 1 ELSE 0 END) as PendingRequests,
    SUM(CASE WHEN pr.Status = 2 THEN 1 ELSE 0 END) as IssuedRequests,
    SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) as InstalledRequests,
    SUM(CASE WHEN pr.Status = 4 THEN 1 ELSE 0 END) as CancelledRequests,
    SUM(pr.RequestedQuantity) as TotalRequestedQuantity,
    SUM(CASE WHEN pr.ActualQuantity IS NOT NULL THEN pr.ActualQuantity ELSE 0 END) as TotalActualQuantity,
    SUM(CASE WHEN pr.TotalCost IS NOT NULL THEN pr.TotalCost ELSE 0 END) as TotalCost,
    CASE 
        WHEN COUNT(pr.Id) = 0 THEN 100
        ELSE CAST(SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(pr.Id) AS DECIMAL(5,2))
    END as CompletionPercentage
FROM [dbo].[RepairOrders] ro
    LEFT JOIN [dbo].[RepairOrderPartRequests] pr ON ro.Id = pr.RepairOrderId
GROUP BY ro.Id, ro.OrderNumber, ro.FaultDescription
GO

PRINT '✓ 维修单零件统计视图创建成功'

-- =============================================
-- 第五步：验证修复结果
-- =============================================

PRINT '5. 验证修复结果...'

-- 检查表是否存在
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND type in (N'U'))
BEGIN
    PRINT '✓ RepairOrderPartRequests表存在'
END
ELSE
BEGIN
    PRINT '❌ RepairOrderPartRequests表不存在'
END

-- 检查视图是否创建成功
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
BEGIN
    PRINT '✓ V_RepairOrderPartRequestDetails视图创建成功'
END
ELSE
BEGIN
    PRINT '❌ V_RepairOrderPartRequestDetails视图创建失败'
END

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
BEGIN
    PRINT '✓ V_RepairOrderPartSummary视图创建成功'
END
ELSE
BEGIN
    PRINT '❌ V_RepairOrderPartSummary视图创建失败'
END

-- 检查索引是否创建成功
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalPartNumber')
BEGIN
    PRINT '✓ 外部零件编号索引存在'
END

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalRequisitionDetailId')
BEGIN
    PRINT '✓ 外部领用单明细ID索引存在'
END

-- =============================================
-- 完成修复
-- =============================================

PRINT ''
PRINT '========================================'
PRINT '零件申请表迁移修复完成！'
PRINT ''
PRINT '修复内容总结：'
PRINT '- 删除了有问题的视图'
PRINT '- 删除了重复的索引'
PRINT '- 重新创建了正确的索引'
PRINT '- 重新创建了正确的视图（修复字段名）'
PRINT ''
PRINT '字段名修复：'
PRINT '- RepairOrders.Title → RepairOrders.FaultDescription'
PRINT '- Users.Name → Users.DisplayName'
PRINT ''
PRINT '现在可以正常使用零件申请功能了！'
PRINT '========================================'

-- 显示表结构信息
PRINT ''
PRINT 'RepairOrderPartRequests 表字段信息：'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CASE 
        WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
        WHEN NUMERIC_PRECISION IS NOT NULL THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + ',' + CAST(NUMERIC_SCALE AS VARCHAR(10))
        ELSE 'N/A'
    END AS LENGTH_PRECISION
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairOrderPartRequests'
ORDER BY ORDINAL_POSITION;
