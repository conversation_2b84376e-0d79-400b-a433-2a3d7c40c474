using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 维修单零件申请服务实现
    /// </summary>
    public class RepairOrderPartRequestService : IRepairOrderPartRequestService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<RepairOrderPartRequestService> _logger;

        public RepairOrderPartRequestService(
            DatabaseContext dbContext,
            ILogger<RepairOrderPartRequestService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region 基础CRUD操作

        public async Task<List<RepairOrderPartRequest>> GetPartRequestsByRepairOrderIdAsync(int repairOrderId)
        {
            try
            {
                return await _dbContext.RepairOrderPartRequests
                    .Includes(r => r.Requester)
                    .Includes(r => r.Issuer)
                    .Includes(r => r.Installer)
                    .Where(r => r.RepairOrderId == repairOrderId)
                    .OrderBy(r => r.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修单零件申请失败: RepairOrderId={RepairOrderId}", repairOrderId);
                throw;
            }
        }

        public async Task<RepairOrderPartRequest?> GetPartRequestByIdAsync(int id)
        {
            try
            {
                return await _dbContext.RepairOrderPartRequests
                    .Includes(r => r.RepairOrder)
                    .Includes(r => r.Requester)
                    .Includes(r => r.Issuer)
                    .Includes(r => r.Installer)
                    .Where(r => r.Id == id)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据ID获取零件申请失败: Id={Id}", id);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreatePartRequestAsync(RepairOrderPartRequest partRequest)
        {
            try
            {
                partRequest.CreatedAt = DateTime.Now;
                partRequest.RequestedAt = DateTime.Now;
                partRequest.Status = 1; // 申请中

                var result = await _dbContext.Db.Insertable(partRequest).ExecuteReturnIdentityAsync();

                _logger.LogInformation("创建零件申请成功: Id={Id}, RepairOrderId={RepairOrderId}, PartName={PartName}", 
                    result, partRequest.RepairOrderId, partRequest.PartName);

                return (true, string.Empty, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建零件申请失败: RepairOrderId={RepairOrderId}, PartName={PartName}", 
                    partRequest.RepairOrderId, partRequest.PartName);
                return (false, $"创建零件申请失败: {ex.Message}", null);
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, List<int> CreatedIds)> CreatePartRequestsBatchAsync(int repairOrderId, List<PartReplacementRequestDto> partRequests)
        {
            var createdIds = new List<int>();
            
            try
            {
                // 开始事务
                _dbContext.Db.Ado.BeginTran();

                foreach (var dto in partRequests)
                {
                    var entity = dto.ToEntity();
                    entity.RepairOrderId = repairOrderId;
                    entity.CreatedAt = DateTime.Now;
                    entity.RequestedAt = DateTime.Now;
                    entity.Status = 1; // 申请中

                    var id = await _dbContext.Db.Insertable(entity).ExecuteReturnIdentityAsync();
                    createdIds.Add(id);
                }

                _dbContext.Db.Ado.CommitTran();

                _logger.LogInformation("批量创建零件申请成功: RepairOrderId={RepairOrderId}, Count={Count}", 
                    repairOrderId, partRequests.Count);

                return (true, string.Empty, createdIds);
            }
            catch (Exception ex)
            {
                _dbContext.Db.Ado.RollbackTran();
                _logger.LogError(ex, "批量创建零件申请失败: RepairOrderId={RepairOrderId}", repairOrderId);
                return (false, $"批量创建零件申请失败: {ex.Message}", createdIds);
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdatePartRequestAsync(RepairOrderPartRequest partRequest)
        {
            try
            {
                partRequest.UpdatedAt = DateTime.Now;
                var result = await _dbContext.Db.Updateable(partRequest).ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("更新零件申请成功: Id={Id}", partRequest.Id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "零件申请不存在或未发生变更");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新零件申请失败: Id={Id}", partRequest.Id);
                return (false, $"更新零件申请失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> DeletePartRequestAsync(int id)
        {
            try
            {
                // 检查是否可以删除
                var (canDelete, reason) = await CanDeletePartRequestAsync(id);
                if (!canDelete)
                {
                    return (false, reason);
                }

                var result = await _dbContext.Db.Deleteable<RepairOrderPartRequest>()
                    .Where(r => r.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("删除零件申请成功: Id={Id}", id);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "零件申请不存在");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除零件申请失败: Id={Id}", id);
                return (false, $"删除零件申请失败: {ex.Message}");
            }
        }

        #endregion

        #region 状态管理



        public async Task<(bool IsSuccess, string ErrorMessage)> ConfirmPartIssueAsync(int id, int issuerId, string warehouseOrderNumber, decimal? unitPrice = null, decimal? totalCost = null)
        {
            try
            {
                var partRequest = await GetPartRequestByIdAsync(id);
                if (partRequest == null)
                {
                    return (false, "零件申请不存在");
                }

                if (partRequest.Status != 1)
                {
                    return (false, "只有申请中的记录才能发放");
                }

                // 如果没有提供总成本，根据单价和数量计算
                if (!totalCost.HasValue && unitPrice.HasValue)
                {
                    totalCost = unitPrice.Value * partRequest.RequestedQuantity;
                }

                var result = await _dbContext.Db.Updateable<RepairOrderPartRequest>()
                    .SetColumns(r => new RepairOrderPartRequest
                    {
                        Status = 2, // 已领用
                        IssuedBy = issuerId,
                        IssuedAt = DateTime.Now,
                        WarehouseOrderNumber = warehouseOrderNumber,
                        UnitPrice = unitPrice ?? r.UnitPrice,
                        TotalCost = totalCost ?? r.TotalCost,
                        UpdatedAt = DateTime.Now
                    })
                    .Where(r => r.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("确认零件发放成功: Id={Id}, IssuerId={IssuerId}, WarehouseOrderNumber={WarehouseOrderNumber}", 
                        id, issuerId, warehouseOrderNumber);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "发放确认失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认零件发放失败: Id={Id}", id);
                return (false, $"确认零件发放失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ConfirmPartInstallationAsync(int id, int installerId)
        {
            try
            {
                var partRequest = await GetPartRequestByIdAsync(id);
                if (partRequest == null)
                {
                    return (false, "零件申请不存在");
                }

                if (partRequest.Status != 2)
                {
                    return (false, "只有已领用的记录才能安装");
                }

                var result = await _dbContext.Db.Updateable<RepairOrderPartRequest>()
                    .SetColumns(r => new RepairOrderPartRequest
                    {
                        Status = 3, // 已安装
                        InstalledBy = installerId,
                        InstalledAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    })
                    .Where(r => r.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("确认零件安装成功: Id={Id}, InstallerId={InstallerId}", id, installerId);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "安装确认失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认零件安装失败: Id={Id}", id);
                return (false, $"确认零件安装失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CancelPartRequestAsync(int id, int operatorId, string reason)
        {
            try
            {
                var partRequest = await GetPartRequestByIdAsync(id);
                if (partRequest == null)
                {
                    return (false, "零件申请不存在");
                }

                if (partRequest.Status == 3 || partRequest.Status == 4)
                {
                    return (false, "已完成或已取消的记录不能再次取消");
                }

                var result = await _dbContext.Db.Updateable<RepairOrderPartRequest>()
                    .SetColumns(r => new RepairOrderPartRequest
                    {
                        Status = 4, // 已取消
                        UpdatedAt = DateTime.Now,
                        Remark = reason
                    })
                    .Where(r => r.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("取消零件申请成功: Id={Id}, OperatorId={OperatorId}, Reason={Reason}", 
                        id, operatorId, reason);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "取消失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消零件申请失败: Id={Id}", id);
                return (false, $"取消零件申请失败: {ex.Message}");
            }
        }

        #endregion

        #region 外部系统集成

        public async Task<RepairOrderPartRequest?> GetPartRequestByExternalPartNumberAsync(string externalPartNumber)
        {
            try
            {
                return await _dbContext.RepairOrderPartRequests
                    .Includes(r => r.RepairOrder)
                    .Where(r => r.ExternalPartNumber == externalPartNumber)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据外部零件编号获取申请失败: ExternalPartNumber={ExternalPartNumber}", externalPartNumber);
                throw;
            }
        }

        public async Task<RepairOrderPartRequest?> GetPartRequestByExternalRequisitionDetailIdAsync(string externalRequisitionDetailId)
        {
            try
            {
                return await _dbContext.RepairOrderPartRequests
                    .Includes(r => r.RepairOrder)
                    .Where(r => r.ExternalRequisitionDetailId == externalRequisitionDetailId)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "根据外部领用单明细ID获取申请失败: ExternalRequisitionDetailId={ExternalRequisitionDetailId}", externalRequisitionDetailId);
                throw;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> UpdateFromExternalSystemAsync(
            int id,
            string? externalPartNumber = null,
            string? externalRequisitionDetailId = null,
            int? actualQuantity = null,
            string? actualPartName = null,
            string? actualSpecification = null,
            int? status = null)
        {
            try
            {
                var partRequest = await GetPartRequestByIdAsync(id);
                if (partRequest == null)
                {
                    return (false, "零件申请不存在");
                }

                // 验证状态变更是否合法
                if (status.HasValue && !IsValidStatusTransition(partRequest.Status, status.Value))
                {
                    return (false, $"非法的状态变更: {partRequest.Status} -> {status.Value}");
                }

                // 构建更新对象
                var updateObj = new RepairOrderPartRequest
                {
                    Id = id,
                    UpdatedAt = DateTime.Now
                };

                if (!string.IsNullOrWhiteSpace(externalPartNumber))
                    updateObj.ExternalPartNumber = externalPartNumber;

                if (!string.IsNullOrWhiteSpace(externalRequisitionDetailId))
                    updateObj.ExternalRequisitionDetailId = externalRequisitionDetailId;

                if (actualQuantity.HasValue)
                    updateObj.ActualQuantity = actualQuantity.Value;

                if (!string.IsNullOrWhiteSpace(actualPartName))
                    updateObj.ActualPartName = actualPartName;

                if (!string.IsNullOrWhiteSpace(actualSpecification))
                    updateObj.ActualSpecification = actualSpecification;

                if (status.HasValue)
                    updateObj.Status = status.Value;

                var result = await _dbContext.Db.Updateable(updateObj)
                    .UpdateColumns(r => new { r.ExternalPartNumber, r.ExternalRequisitionDetailId, r.ActualQuantity, r.ActualPartName, r.ActualSpecification, r.Status, r.UpdatedAt })
                    .Where(r => r.Id == id)
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    _logger.LogInformation("外部系统回写成功: Id={Id}, ExternalPartNumber={ExternalPartNumber}, Status={Status}",
                        id, externalPartNumber, status);
                    return (true, string.Empty);
                }
                else
                {
                    return (false, "外部系统回写失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "外部系统回写失败: Id={Id}", id);
                return (false, $"外部系统回写失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage, List<int> UpdatedIds)> BatchUpdateFromExternalSystemAsync(List<ExternalPartUpdateDto> updates)
        {
            var updatedIds = new List<int>();

            try
            {
                _dbContext.Db.Ado.BeginTran();

                foreach (var update in updates)
                {
                    var (isSuccess, errorMessage) = await UpdateFromExternalSystemAsync(
                        update.Id,
                        update.ExternalPartNumber,
                        update.ExternalRequisitionDetailId,
                        update.ActualQuantity,
                        update.ActualPartName,
                        update.ActualSpecification,
                        update.Status);

                    if (isSuccess)
                    {
                        updatedIds.Add(update.Id);
                    }
                    else
                    {
                        _dbContext.Db.Ado.RollbackTran();
                        return (false, $"批量更新失败，记录ID {update.Id}: {errorMessage}", updatedIds);
                    }
                }

                _dbContext.Db.Ado.CommitTran();

                _logger.LogInformation("批量外部系统回写成功: UpdatedCount={UpdatedCount}", updatedIds.Count);
                return (true, string.Empty, updatedIds);
            }
            catch (Exception ex)
            {
                _dbContext.Db.Ado.RollbackTran();
                _logger.LogError(ex, "批量外部系统回写失败");
                return (false, $"批量外部系统回写失败: {ex.Message}", updatedIds);
            }
        }

        #endregion

        #region 查询和统计

        public async Task<(List<RepairOrderPartRequest> Items, int TotalCount)> GetPendingPartRequestsAsync(int? status = null, int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                var query = _dbContext.RepairOrderPartRequests
                    .Includes(r => r.RepairOrder)
                    .Includes(r => r.Requester);

                if (status.HasValue)
                {
                    query = query.Where(r => r.Status == status.Value);
                }
                else
                {
                    query = query.Where(r => r.Status < 3); // 未完成的记录
                }

                var totalCount = await query.CountAsync();
                var items = await query
                    .OrderBy(r => r.RequestedAt)
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取待处理零件申请失败");
                throw;
            }
        }

        public async Task<(List<RepairOrderPartRequest> Items, int TotalCount)> GetUserPartRequestsAsync(int userId, int pageIndex = 1, int pageSize = 20)
        {
            try
            {
                var query = _dbContext.RepairOrderPartRequests
                    .Includes(r => r.RepairOrder)
                    .Where(r => r.RequestedBy == userId);

                var totalCount = await query.CountAsync();
                var items = await query
                    .OrderByDescending(r => r.RequestedAt)
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户零件申请失败: UserId={UserId}", userId);
                throw;
            }
        }

        public async Task<(List<RepairOrderPartRequest> Items, int TotalCount)> SearchPartRequestsAsync(
            string? keyword = null,
            int? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageIndex = 1,
            int pageSize = 20)
        {
            try
            {
                var query = _dbContext.RepairOrderPartRequests
                    .Includes(r => r.RepairOrder)
                    .Includes(r => r.Requester);

                if (!string.IsNullOrWhiteSpace(keyword))
                {
                    query = query.Where(r => r.PartName.Contains(keyword) ||
                                           (r.Specification != null && r.Specification.Contains(keyword)) ||
                                           (r.RepairOrder != null && r.RepairOrder.OrderNumber.Contains(keyword)));
                }

                if (status.HasValue)
                {
                    query = query.Where(r => r.Status == status.Value);
                }

                if (startDate.HasValue)
                {
                    query = query.Where(r => r.RequestedAt >= startDate.Value);
                }

                if (endDate.HasValue)
                {
                    query = query.Where(r => r.RequestedAt <= endDate.Value);
                }

                var totalCount = await query.CountAsync();
                var items = await query
                    .OrderByDescending(r => r.RequestedAt)
                    .Skip((pageIndex - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索零件申请失败");
                throw;
            }
        }

        public async Task<List<PartUsageStatisticsDto>> GetPartUsageStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var statistics = await _dbContext.RepairOrderPartRequests
                    .Where(r => r.Status == 3 && // 已安装
                               r.InstalledAt >= startDate &&
                               r.InstalledAt <= endDate)
                    .GroupBy(r => new { r.PartName, r.Specification })
                    .Select(g => new PartUsageStatisticsDto
                    {
                        PartName = g.Key.PartName,
                        Specification = g.Key.Specification,
                        UsageCount = SqlFunc.AggregateCount(g.Select(x => x.Id)),
                        TotalQuantity = SqlFunc.AggregateSum(g.Select(x => x.RequestedQuantity)),
                        TotalCost = SqlFunc.AggregateSum(g.Select(x => x.TotalCost ?? 0)),
                        AverageUnitPrice = SqlFunc.AggregateAvg(g.Select(x => x.UnitPrice ?? 0)),
                        LastUsedAt = SqlFunc.AggregateMax(g.Select(x => x.InstalledAt))
                    })
                    .OrderByDescending(s => s.UsageCount)
                    .ToListAsync();

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取零件使用统计失败");
                throw;
            }
        }

        #endregion

        #region 验证

        public async Task<(bool CanEdit, string Reason)> CanEditPartRequestAsync(int id)
        {
            try
            {
                var partRequest = await GetPartRequestByIdAsync(id);
                if (partRequest == null)
                {
                    return (false, "零件申请不存在");
                }

                if (partRequest.Status != 1)
                {
                    return (false, "只有申请中的记录才能编辑");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证零件申请编辑权限失败: Id={Id}", id);
                return (false, $"验证失败: {ex.Message}");
            }
        }

        public async Task<(bool CanDelete, string Reason)> CanDeletePartRequestAsync(int id)
        {
            try
            {
                var partRequest = await GetPartRequestByIdAsync(id);
                if (partRequest == null)
                {
                    return (false, "零件申请不存在");
                }

                if (partRequest.Status != 1)
                {
                    return (false, "只有申请中的记录才能删除");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证零件申请删除权限失败: Id={Id}", id);
                return (false, $"验证失败: {ex.Message}");
            }
        }

        public bool IsValidStatusTransition(int currentStatus, int newStatus)
        {
            // 定义合法的状态转换规则（简化后）
            var validTransitions = new Dictionary<int, List<int>>
            {
                { 1, new List<int> { 2, 4 } }, // 申请中 -> 已领用或已取消
                { 2, new List<int> { 3, 4 } }, // 已领用 -> 已安装或已取消
                { 3, new List<int>() },        // 已安装 -> 无法变更
                { 4, new List<int>() }         // 已取消 -> 无法变更
            };

            return validTransitions.ContainsKey(currentStatus) &&
                   validTransitions[currentStatus].Contains(newStatus);
        }

        #endregion
    }
}
