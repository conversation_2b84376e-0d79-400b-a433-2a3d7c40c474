@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using FluentValidation
@inject IJobTypeService JobTypeService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@JobType" Validation="@(new JobTypeValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="JobType.Code"
                                For="@(() => JobType.Code)"
                                Label="工种编码"
                                Required="true"
                                Immediate="true"
                                Disabled="@IsEdit" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="JobType.Name"
                                For="@(() => JobType.Name)"
                                Label="工种名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="string" @bind-Value="JobType.Category"
                             Label="工种分类"
                             Required="true">
                        @foreach (var category in predefinedCategories)
                        {
                            <MudSelectItem T="string" Value="@category.Key">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@category.Value" Size="Size.Small" />
                                    <MudText>@category.Key</MudText>
                                </MudStack>
                            </MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="JobType.SortOrder"
                                   For="@(() => JobType.SortOrder)"
                                   Label="排序号"
                                   Min="0"
                                   Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="JobType.Description"
                                For="@(() => JobType.Description)"
                                Label="工种描述"
                                Lines="3"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudSwitch T="bool" @bind-Checked="JobType.IsEnabled"
                             Label="启用状态"
                             Color="Color.Primary" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@saving">
            @if (saving)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public JobType JobType { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; } = false;

    private MudForm form = null!;
    private bool saving = false;
    private string originalCode = string.Empty;

    private readonly Dictionary<string, string> predefinedCategories = new()
    {
        { "生产", Icons.Material.Filled.PrecisionManufacturing },
        { "维修", Icons.Material.Filled.Engineering },
        { "管理", Icons.Material.Filled.ManageAccounts },
        { "支持", Icons.Material.Filled.Support },
        { "质检", Icons.Material.Filled.FactCheck },
        { "安全", Icons.Material.Filled.Security },
        { "技术", Icons.Material.Filled.Science },
        { "物流", Icons.Material.Filled.LocalShipping }
    };

    protected override async Task OnInitializedAsync()
    {
        if (!IsEdit)
        {
            JobType.SortOrder = 0;
            JobType.IsEnabled = true;
            JobType.Category = "生产"; // 默认分类
        }
        else
        {
            originalCode = JobType.Code;
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        await form.Validate();
        if (!form.IsValid) return;

        saving = true;
        try
        {
            // 检查编码是否重复
            if (!IsEdit || !string.Equals(JobType.Code, originalCode, StringComparison.OrdinalIgnoreCase))
            {
                var exists = await JobTypeService.ExistsByCodeAsync(JobType.Code, IsEdit ? JobType.Id : null);
                if (exists)
                {
                    Snackbar.Add("工种编码已存在", Severity.Error);
                    return;
                }
            }

            bool result;
            
            if (IsEdit)
            {
                JobType.UpdatedAt = DateTime.Now;
                result = await JobTypeService.UpdateJobTypeAsync(JobType);
            }
            else
            {
                result = await JobTypeService.CreateJobTypeAsync(JobType);
            }

            if (result)
            {
                Snackbar.Add($"工种{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add("操作失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    public class JobTypeValidator : AbstractValidator<JobType>
    {
        public JobTypeValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("工种编码不能为空")
                .MaximumLength(50).WithMessage("工种编码长度不能超过50个字符")
                .Matches(@"^[A-Za-z0-9_-]+$").WithMessage("工种编码只能包含字母、数字、下划线和连字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("工种名称不能为空")
                .MaximumLength(100).WithMessage("工种名称长度不能超过100个字符");

            RuleFor(x => x.Category)
                .NotEmpty().WithMessage("工种分类不能为空")
                .MaximumLength(50).WithMessage("工种分类长度不能超过50个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("工种描述长度不能超过500个字符");

            RuleFor(x => x.SortOrder)
                .GreaterThanOrEqualTo(0).WithMessage("排序号不能为负数");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<JobType>.CreateWithOptions((JobType)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
