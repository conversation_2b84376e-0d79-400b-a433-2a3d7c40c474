@using CoreHub.Shared.Models.Dto

<MudCard Class="mb-4">
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                零件更换记录
                @if (PartRecords.HasAnyParts)
                {
                    <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                        @PartRecords.TotalCount 项
                    </MudChip>
                }
            </MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            @if (ShowProgress && PartRecords.HasAnyParts)
            {
                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                        进度: @PartRecords.CompletedCount/@PartRecords.TotalCount
                    </MudText>
                    <MudProgressLinear Color="Color.Primary" 
                                       Value="@((double)PartRecords.CompletedCount / PartRecords.TotalCount * 100)" 
                                       Size="Size.Small" 
                                       Class="flex-grow-1" 
                                       Style="width: 100px;" />
                </MudStack>
            }
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent>
        @if (!PartRecords.HasAnyParts)
        {
            <MudAlert Severity="Severity.Info" Class="mb-4">
                <MudText>此维修单无零件更换记录。</MudText>
            </MudAlert>
        }
        else
        {
            @if (ShowSummary)
            {
                <!-- 统计信息 -->
                <MudGrid Class="mb-4">
                    <MudItem xs="6" sm="3">
                        <MudPaper Class="pa-3 text-center">
                            <MudText Typo="Typo.h6" Color="Color.Primary">@PartRecords.TotalCount</MudText>
                            <MudText Typo="Typo.caption">总记录数</MudText>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="6" sm="3">
                        <MudPaper Class="pa-3 text-center">
                            <MudText Typo="Typo.h6" Color="Color.Warning">@PartRecords.PendingCount</MudText>
                            <MudText Typo="Typo.caption">申请中</MudText>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="6" sm="3">
                        <MudPaper Class="pa-3 text-center">
                            <MudText Typo="Typo.h6" Color="Color.Success">@PartRecords.CompletedCount</MudText>
                            <MudText Typo="Typo.caption">已完成</MudText>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="6" sm="3">
                        <MudPaper Class="pa-3 text-center">
                            <MudText Typo="Typo.h6" Color="Color.Info">@PartRecords.TotalRequestedQuantity</MudText>
                            <MudText Typo="Typo.caption">总数量</MudText>
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            }

            <!-- 零件列表 -->
            @if (ShowAsTable)
            {
                <MudTable Items="@PartRecords.PartRequests" 
                          Dense="@Dense" 
                          Hover="true" 
                          Breakpoint="Breakpoint.Sm"
                          Class="mb-4">
                    <HeaderContent>
                        <MudTh>零件名称</MudTh>
                        <MudTh>规格型号</MudTh>
                        <MudTh>数量</MudTh>
                        <MudTh>状态</MudTh>
                        @if (ShowDetails)
                        {
                            <MudTh>更换原因</MudTh>
                            <MudTh>创建时间</MudTh>
                        }
                        @if (ShowExternalFields)
                        {
                            <MudTh>外部编号</MudTh>
                            <MudTh>实际信息</MudTh>
                        }
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="零件名称">
                            <MudText Typo="Typo.body2">@context.PartName</MudText>
                        </MudTd>
                        <MudTd DataLabel="规格型号">
                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                @(string.IsNullOrWhiteSpace(context.Specification) ? "-" : context.Specification)
                            </MudText>
                        </MudTd>
                        <MudTd DataLabel="数量">
                            <MudText Typo="Typo.body2">@context.QuantityDescription</MudText>
                        </MudTd>
                        <MudTd DataLabel="状态">
                            <MudChip T="string" 
                                     Color="@GetStatusColor(context.Status)" 
                                     Size="Size.Small">
                                @context.StatusName
                            </MudChip>
                        </MudTd>
                        @if (ShowDetails)
                        {
                            <MudTd DataLabel="更换原因">
                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                    @(string.IsNullOrWhiteSpace(context.Reason) ? "-" : 
                                      (context.Reason.Length > 30 ? context.Reason.Substring(0, 30) + "..." : context.Reason))
                                </MudText>
                            </MudTd>
                            <MudTd DataLabel="创建时间">
                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                    @context.CreatedAt.ToString("MM-dd HH:mm")
                                </MudText>
                            </MudTd>
                        }
                        @if (ShowExternalFields)
                        {
                            <MudTd DataLabel="外部编号">
                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                    @(string.IsNullOrWhiteSpace(context.ExternalPartNumber) ? "-" : context.ExternalPartNumber)
                                </MudText>
                            </MudTd>
                            <MudTd DataLabel="实际信息">
                                @if (context.ActualQuantity.HasValue || !string.IsNullOrWhiteSpace(context.ActualPartName))
                                {
                                    <MudStack Spacing="0">
                                        @if (!string.IsNullOrWhiteSpace(context.ActualPartName))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                名称: @context.ActualPartName
                                            </MudText>
                                        }
                                        @if (context.ActualQuantity.HasValue)
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                数量: @context.ActualQuantity
                                            </MudText>
                                        }
                                        @if (!string.IsNullOrWhiteSpace(context.ActualSpecification))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                规格: @context.ActualSpecification
                                            </MudText>
                                        }
                                    </MudStack>
                                }
                                else
                                {
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">-</MudText>
                                }
                            </MudTd>
                        }
                    </RowTemplate>
                </MudTable>
            }
            else
            {
                <!-- 卡片列表显示 -->
                <MudStack Spacing="2">
                    @foreach (var part in PartRecords.PartRequests)
                    {
                        <MudPaper Class="pa-3" Elevation="1">
                            <MudGrid AlignItems="AlignItems.Center">
                                <MudItem xs="12" sm="6" md="4">
                                    <MudStack Spacing="1">
                                        <MudText Typo="Typo.body1">@part.PartName</MudText>
                                        @if (!string.IsNullOrWhiteSpace(part.Specification))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                规格: @part.Specification
                                            </MudText>
                                        }
                                    </MudStack>
                                </MudItem>
                                <MudItem xs="6" sm="3" md="2">
                                    <MudText Typo="Typo.body2">@part.QuantityDescription</MudText>
                                </MudItem>
                                <MudItem xs="6" sm="3" md="2">
                                    <MudChip T="string" 
                                             Color="@GetStatusColor(part.Status)" 
                                             Size="Size.Small">
                                        @part.StatusName
                                    </MudChip>
                                </MudItem>
                                @if (ShowDetails)
                                {
                                    <MudItem xs="12" md="4">
                                        @if (!string.IsNullOrWhiteSpace(part.Reason))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                原因: @part.Reason
                                            </MudText>
                                        }
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                                            @part.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                                        </MudText>
                                    </MudItem>
                                }
                            </MudGrid>
                        </MudPaper>
                    }
                </MudStack>
            }
        }
    </MudCardContent>
</MudCard>

@code {
    [Parameter] public PartReplacementRequestCollectionDto PartRecords { get; set; } = new();
    [Parameter] public bool ShowSummary { get; set; } = true;
    [Parameter] public bool ShowProgress { get; set; } = true;
    [Parameter] public bool ShowAsTable { get; set; } = true;
    [Parameter] public bool ShowDetails { get; set; } = true;
    [Parameter] public bool ShowExternalFields { get; set; } = false;
    [Parameter] public bool Dense { get; set; } = true;

    private Color GetStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Warning,  // 申请中
            2 => Color.Primary,  // 已领用
            3 => Color.Success,  // 已安装
            4 => Color.Error,    // 已取消
            _ => Color.Default
        };
    }
}
