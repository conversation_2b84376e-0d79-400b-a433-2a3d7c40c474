-- =============================================
-- 完整修复零件申请表脚本
-- 创建日期: 2025-01-07
-- 版本: 2.2.0
-- 描述: 去掉审批字段，修复所有问题
-- =============================================

USE [EquipmentManagement]
GO

PRINT '开始完整修复零件申请表...'
PRINT '========================================'

-- =============================================
-- 第一步：删除有问题的视图和索引
-- =============================================

PRINT '1. 清理旧的视图和索引...'

-- 删除视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
    DROP VIEW [dbo].[V_RepairOrderPartRequestDetails]

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
    DROP VIEW [dbo].[V_RepairOrderPartSummary]

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrdersWithParts]'))
    DROP VIEW [dbo].[V_RepairOrdersWithParts]

-- 删除重复索引
IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalPartNumber')
    DROP INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests]

IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalRequisitionDetailId')
    DROP INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests]

PRINT '✓ 清理完成'

-- =============================================
-- 第二步：删除不需要的审批字段
-- =============================================

PRINT '2. 删除审批相关字段...'

-- 删除ApprovedBy字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedBy')
BEGIN
    ALTER TABLE [dbo].[RepairOrderPartRequests] DROP COLUMN [ApprovedBy]
    PRINT '✓ 删除ApprovedBy字段'
END

-- 删除ApprovedAt字段
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedAt')
BEGIN
    ALTER TABLE [dbo].[RepairOrderPartRequests] DROP COLUMN [ApprovedAt]
    PRINT '✓ 删除ApprovedAt字段'
END

-- =============================================
-- 第三步：重新创建索引
-- =============================================

PRINT '3. 重新创建索引...'

CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RepairOrderId] ON [dbo].[RepairOrderPartRequests] ([RepairOrderId])
CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_Status] ON [dbo].[RepairOrderPartRequests] ([Status])
CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RequestedAt] ON [dbo].[RepairOrderPartRequests] ([RequestedAt])

CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests] ([ExternalPartNumber])
WHERE [ExternalPartNumber] IS NOT NULL

CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests] ([ExternalRequisitionDetailId])
WHERE [ExternalRequisitionDetailId] IS NOT NULL

PRINT '✓ 索引创建完成'

-- =============================================
-- 第四步：创建正确的视图（无审批字段）
-- =============================================

PRINT '4. 创建正确的视图...'

-- 零件申请详细视图
CREATE VIEW [dbo].[V_RepairOrderPartRequestDetails]
AS
SELECT 
    pr.Id,
    pr.RepairOrderId,
    ro.OrderNumber as RepairOrderNumber,
    ro.FaultDescription as RepairOrderDescription,
    pr.PartName,
    pr.Specification,
    pr.RequestedQuantity,
    pr.Unit,
    pr.Reason,
    pr.Remark,
    pr.Status,
    CASE pr.Status
        WHEN 1 THEN N'申请中'
        WHEN 2 THEN N'已领用'
        WHEN 3 THEN N'已安装'
        WHEN 4 THEN N'已取消'
        ELSE N'未知'
    END AS StatusName,
    pr.RequestedBy,
    ru.DisplayName as RequestedByName,
    pr.RequestedAt,
    pr.IssuedBy,
    iu.DisplayName as IssuedByName,
    pr.IssuedAt,
    pr.InstalledBy,
    inu.DisplayName as InstalledByName,
    pr.InstalledAt,
    pr.ExternalPartNumber,
    pr.ExternalRequisitionDetailId,
    pr.ActualQuantity,
    pr.ActualPartName,
    pr.ActualSpecification,
    pr.UnitPrice,
    pr.TotalCost,
    pr.WarehouseOrderNumber,
    pr.CreatedAt,
    pr.UpdatedAt
FROM [dbo].[RepairOrderPartRequests] pr
    INNER JOIN [dbo].[RepairOrders] ro ON pr.RepairOrderId = ro.Id
    LEFT JOIN [dbo].[Users] ru ON pr.RequestedBy = ru.Id
    LEFT JOIN [dbo].[Users] iu ON pr.IssuedBy = iu.Id
    LEFT JOIN [dbo].[Users] inu ON pr.InstalledBy = inu.Id
GO

-- 维修单零件统计视图
CREATE VIEW [dbo].[V_RepairOrderPartSummary]
AS
SELECT 
    ro.Id as RepairOrderId,
    ro.OrderNumber,
    ro.FaultDescription as Description,
    COUNT(pr.Id) as TotalPartRequests,
    SUM(CASE WHEN pr.Status = 1 THEN 1 ELSE 0 END) as PendingRequests,
    SUM(CASE WHEN pr.Status = 2 THEN 1 ELSE 0 END) as IssuedRequests,
    SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) as InstalledRequests,
    SUM(CASE WHEN pr.Status = 4 THEN 1 ELSE 0 END) as CancelledRequests,
    SUM(pr.RequestedQuantity) as TotalRequestedQuantity,
    SUM(CASE WHEN pr.ActualQuantity IS NOT NULL THEN pr.ActualQuantity ELSE 0 END) as TotalActualQuantity,
    SUM(CASE WHEN pr.TotalCost IS NOT NULL THEN pr.TotalCost ELSE 0 END) as TotalCost,
    CASE 
        WHEN COUNT(pr.Id) = 0 THEN 100
        ELSE CAST(SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(pr.Id) AS DECIMAL(5,2))
    END as CompletionPercentage
FROM [dbo].[RepairOrders] ro
    LEFT JOIN [dbo].[RepairOrderPartRequests] pr ON ro.Id = pr.RepairOrderId
GROUP BY ro.Id, ro.OrderNumber, ro.FaultDescription
GO

PRINT '✓ 视图创建完成'

-- =============================================
-- 第五步：验证修复结果
-- =============================================

PRINT '5. 验证修复结果...'

-- 检查审批字段是否已删除
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedBy')
    PRINT '✓ ApprovedBy字段已删除'
ELSE
    PRINT '❌ ApprovedBy字段仍然存在'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = 'ApprovedAt')
    PRINT '✓ ApprovedAt字段已删除'
ELSE
    PRINT '❌ ApprovedAt字段仍然存在'

-- 检查视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
    PRINT '✓ V_RepairOrderPartRequestDetails视图创建成功'

IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
    PRINT '✓ V_RepairOrderPartSummary视图创建成功'

-- 测试视图查询
BEGIN TRY
    SELECT TOP 1 * FROM V_RepairOrderPartRequestDetails
    PRINT '✓ 详细视图查询正常'
END TRY
BEGIN CATCH
    PRINT '❌ 详细视图查询失败: ' + ERROR_MESSAGE()
END CATCH

BEGIN TRY
    SELECT TOP 1 * FROM V_RepairOrderPartSummary
    PRINT '✓ 统计视图查询正常'
END TRY
BEGIN CATCH
    PRINT '❌ 统计视图查询失败: ' + ERROR_MESSAGE()
END CATCH

-- =============================================
-- 完成修复
-- =============================================

PRINT ''
PRINT '========================================'
PRINT '零件申请表完整修复完成！'
PRINT ''
PRINT '修复内容总结：'
PRINT '- 删除了审批相关字段（ApprovedBy, ApprovedAt）'
PRINT '- 修复了字段名错误'
PRINT '- 重新创建了正确的索引'
PRINT '- 重新创建了正确的视图'
PRINT '- 简化了状态流程（申请中→已领用→已安装）'
PRINT ''
PRINT '当前状态定义：'
PRINT '1 = 申请中'
PRINT '2 = 已领用'
PRINT '3 = 已安装'
PRINT '4 = 已取消'
PRINT ''
PRINT '现在可以正常使用零件申请功能了！'
PRINT '========================================'

-- 显示最终表结构
PRINT ''
PRINT 'RepairOrderPartRequests 表最终结构：'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CASE 
        WHEN CHARACTER_MAXIMUM_LENGTH IS NOT NULL THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
        WHEN NUMERIC_PRECISION IS NOT NULL THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + ',' + CAST(NUMERIC_SCALE AS VARCHAR(10))
        ELSE 'N/A'
    END AS LENGTH_PRECISION
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairOrderPartRequests'
ORDER BY ORDINAL_POSITION;
