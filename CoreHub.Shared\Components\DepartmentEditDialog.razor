@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using FluentValidation
@inject IDepartmentService DepartmentService
@inject IDepartmentTypeService DepartmentTypeService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@Department" Validation="@(new DepartmentValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Department.Code"
                                For="@(() => Department.Code)"
                                Label="部门编码"
                                Required="true"
                                Immediate="true"
                                Disabled="@IsEdit" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Department.Name"
                                For="@(() => Department.Name)"
                                Label="部门名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Department.Description"
                                For="@(() => Department.Description)"
                                Label="部门描述"
                                Lines="3"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int?" @bind-Value="Department.DepartmentTypeId"
                             Label="部门类型"
                             Clearable="true">
                        @foreach (var type in departmentTypes)
                        {
                            <MudSelectItem T="int?" Value="@type.Id">@type.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int?" @bind-Value="Department.ParentId"
                             Label="上级部门"
                             Clearable="true">
                        @foreach (var dept in parentDepartments)
                        {
                            <MudSelectItem T="int?" Value="@(dept.Id)">@dept.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="Department.SortOrder"
                                   For="@(() => Department.SortOrder)"
                                   Label="排序号"
                                   Min="0"
                                   Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudSwitch T="bool" @bind-Checked="Department.IsEnabled"
                             Label="启用状态"
                             Color="Color.Primary" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Department.Remark"
                                For="@(() => Department.Remark)"
                                Label="备注"
                                Lines="2"
                                Immediate="true" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@saving">
            @if (saving)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public Department Department { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; } = false;

    private MudForm form = null!;
    private bool saving = false;
    private List<Department> parentDepartments = new();
    private List<DepartmentType> departmentTypes = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadParentDepartments();
        await LoadDepartmentTypes();

        if (!IsEdit)
        {
            Department.Level = 1;
            Department.SortOrder = 0;
            Department.IsEnabled = true;
        }
    }

    private async Task LoadParentDepartments()
    {
        try
        {
            var allDepartments = await DepartmentService.GetAllDepartmentsAsync();
            
            // 如果是编辑模式，排除自己和自己的子部门
            if (IsEdit)
            {
                parentDepartments = allDepartments
                    .Where(d => d.Id != Department.Id && !IsChildDepartment(d, Department.Id))
                    .ToList();
            }
            else
            {
                parentDepartments = allDepartments.ToList();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载上级部门失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDepartmentTypes()
    {
        try
        {
            departmentTypes = await DepartmentTypeService.GetEnabledDepartmentTypesAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部门类型失败: {ex.Message}", Severity.Error);
        }
    }

    private bool IsChildDepartment(Department department, int parentId)
    {
        // 简单的递归检查，实际项目中可能需要更复杂的逻辑
        return department.ParentId == parentId;
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        await form.Validate();
        if (!form.IsValid) return;

        saving = true;
        try
        {
            // 设置级别
            if (Department.ParentId.HasValue)
            {
                var parent = parentDepartments.FirstOrDefault(d => d.Id == Department.ParentId.Value);
                Department.Level = parent?.Level + 1 ?? 1;
            }
            else
            {
                Department.Level = 1;
            }

            (bool IsSuccess, string ErrorMessage) result;
            
            if (IsEdit)
            {
                Department.UpdatedAt = DateTime.Now;
                result = await DepartmentService.UpdateDepartmentAsync(Department);
            }
            else
            {
                result = await DepartmentService.CreateDepartmentAsync(Department);
            }

            if (result.IsSuccess)
            {
                Snackbar.Add($"部门{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    public class DepartmentValidator : AbstractValidator<Department>
    {
        public DepartmentValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("部门编码不能为空")
                .MaximumLength(50).WithMessage("部门编码长度不能超过50个字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("部门名称不能为空")
                .MaximumLength(100).WithMessage("部门名称长度不能超过100个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("部门描述长度不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<Department>.CreateWithOptions((Department)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
