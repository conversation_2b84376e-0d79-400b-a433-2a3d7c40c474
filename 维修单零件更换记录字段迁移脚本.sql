-- =============================================
-- 维修单零件更换记录字段迁移脚本
-- 创建日期: 2025-01-07
-- 版本: 1.0.0
-- 描述: 为RepairOrders表添加零件更换记录JSON字段
-- =============================================

USE [EquipmentManagement]
GO

PRINT '开始执行维修单零件更换记录字段迁移...'
PRINT '========================================'

-- =============================================
-- 第一步：检查表是否存在
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND type in (N'U'))
BEGIN
    PRINT '❌ RepairOrders表不存在，请先创建基础表结构'
    RETURN
END

PRINT '✓ RepairOrders表存在，继续执行迁移'

-- =============================================
-- 第二步：添加零件更换记录字段
-- =============================================

PRINT '1. 检查PartReplacementRecordsJson字段是否存在...'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND name = 'PartReplacementRecordsJson')
BEGIN
    PRINT '2. 添加PartReplacementRecordsJson字段...'
    
    ALTER TABLE [dbo].[RepairOrders]
    ADD [PartReplacementRecordsJson] [nvarchar](max) NULL
    
    PRINT '✓ PartReplacementRecordsJson字段添加成功'
END
ELSE
BEGIN
    PRINT '○ PartReplacementRecordsJson字段已存在'
END

-- =============================================
-- 第三步：添加字段注释
-- =============================================

PRINT '3. 添加字段注释...'

IF NOT EXISTS (SELECT * FROM sys.extended_properties 
               WHERE major_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
               AND minor_id = (SELECT column_id FROM sys.columns 
                              WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
                              AND name = 'PartReplacementRecordsJson')
               AND name = 'MS_Description')
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'零件更换记录（JSON格式存储）',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'RepairOrders',
        @level2type = N'COLUMN',
        @level2name = N'PartReplacementRecordsJson'
    
    PRINT '✓ 字段注释添加成功'
END
ELSE
BEGIN
    PRINT '○ 字段注释已存在'
END

-- =============================================
-- 第四步：创建索引（可选）
-- =============================================

PRINT '4. 检查是否需要创建索引...'

-- 由于是JSON字段，通常不需要传统索引
-- 如果需要查询JSON内容，可以考虑使用JSON索引（SQL Server 2016+）
-- 这里暂时不创建索引，根据实际使用情况再决定

PRINT '○ JSON字段暂不创建索引'

-- =============================================
-- 第五步：验证迁移结果
-- =============================================

PRINT '5. 验证迁移结果...'

-- 检查字段是否正确添加
IF EXISTS (SELECT * FROM sys.columns 
           WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
           AND name = 'PartReplacementRecordsJson'
           AND system_type_id = (SELECT system_type_id FROM sys.types WHERE name = 'nvarchar')
           AND max_length = -1) -- -1 表示 max
BEGIN
    PRINT '✓ 字段验证成功：PartReplacementRecordsJson (nvarchar(max), NULL)'
END
ELSE
BEGIN
    PRINT '❌ 字段验证失败'
    RETURN
END

-- 检查字段注释
IF EXISTS (SELECT * FROM sys.extended_properties 
           WHERE major_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
           AND minor_id = (SELECT column_id FROM sys.columns 
                          WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
                          AND name = 'PartReplacementRecordsJson')
           AND name = 'MS_Description')
BEGIN
    PRINT '✓ 字段注释验证成功'
END
ELSE
BEGIN
    PRINT '⚠ 字段注释验证失败（不影响功能）'
END

-- =============================================
-- 第六步：插入示例数据（可选）
-- =============================================

PRINT '6. 插入示例数据...'

-- 为现有的维修单添加示例零件更换记录（仅用于测试）
-- 在生产环境中，这部分应该注释掉

/*
-- 示例：为第一个维修单添加零件更换记录
IF EXISTS (SELECT TOP 1 * FROM [dbo].[RepairOrders] WHERE PartReplacementRecordsJson IS NULL)
BEGIN
    DECLARE @SampleJson NVARCHAR(MAX) = N'[
        {
            "tempId": "' + CAST(NEWID() AS NVARCHAR(36)) + '",
            "partName": "轴承",
            "specification": "6205-2RS",
            "requestedQuantity": 2,
            "unit": "个",
            "reason": "轴承磨损严重，需要更换",
            "status": 1,
            "createdAt": "' + FORMAT(GETDATE(), 'yyyy-MM-ddTHH:mm:ss.fffZ') + '"
        },
        {
            "tempId": "' + CAST(NEWID() AS NVARCHAR(36)) + '",
            "partName": "密封圈",
            "specification": "O型圈 φ20×2",
            "requestedQuantity": 4,
            "unit": "个",
            "reason": "密封圈老化漏油",
            "status": 1,
            "createdAt": "' + FORMAT(GETDATE(), 'yyyy-MM-ddTHH:mm:ss.fffZ') + '"
        }
    ]'
    
    UPDATE TOP(1) [dbo].[RepairOrders] 
    SET PartReplacementRecordsJson = @SampleJson
    WHERE PartReplacementRecordsJson IS NULL
    
    PRINT '✓ 示例数据插入成功'
END
ELSE
BEGIN
    PRINT '○ 跳过示例数据插入'
END
*/

PRINT '○ 跳过示例数据插入（生产环境）'

-- =============================================
-- 第七步：创建查询视图（可选）
-- =============================================

PRINT '7. 创建查询视图...'

-- 创建一个视图来方便查询包含零件更换记录的维修单
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrdersWithParts]'))
    DROP VIEW [dbo].[V_RepairOrdersWithParts]
GO

CREATE VIEW [dbo].[V_RepairOrdersWithParts]
AS
SELECT 
    ro.Id,
    ro.OrderNumber,
    ro.Title,
    ro.Status,
    ro.CreatedAt,
    ro.UpdatedAt,
    CASE 
        WHEN ro.PartReplacementRecordsJson IS NULL OR ro.PartReplacementRecordsJson = '' 
        THEN 0 
        ELSE 1 
    END AS HasPartReplacementRecords,
    CASE 
        WHEN ro.PartReplacementRecordsJson IS NULL OR ro.PartReplacementRecordsJson = '' 
        THEN 0 
        ELSE (
            SELECT COUNT(*)
            FROM OPENJSON(ro.PartReplacementRecordsJson)
        )
    END AS PartReplacementRecordsCount,
    ro.PartReplacementRecordsJson
FROM [dbo].[RepairOrders] ro
GO

PRINT '✓ 查询视图创建成功'

-- =============================================
-- 第八步：设置权限
-- =============================================

PRINT '8. 设置权限...'

-- 为应用程序用户授权（如果存在）
IF EXISTS (SELECT * FROM sys.database_principals WHERE name = 'CoreHubApp')
BEGIN
    GRANT SELECT, UPDATE ON [dbo].[RepairOrders] TO [CoreHubApp]
    GRANT SELECT ON [dbo].[V_RepairOrdersWithParts] TO [CoreHubApp]
    PRINT '✓ 应用程序用户权限设置完成'
END
ELSE
BEGIN
    PRINT '○ 应用程序用户不存在，跳过权限设置'
END

-- =============================================
-- 完成迁移
-- =============================================

PRINT ''
PRINT '========================================'
PRINT '维修单零件更换记录字段迁移完成！'
PRINT ''
PRINT '迁移内容总结：'
PRINT '- 添加了 PartReplacementRecordsJson 字段到 RepairOrders 表'
PRINT '- 字段类型：nvarchar(max)，允许NULL'
PRINT '- 添加了字段注释'
PRINT '- 创建了查询视图 V_RepairOrdersWithParts'
PRINT '- 设置了相应的权限'
PRINT ''
PRINT '使用说明：'
PRINT '1. PartReplacementRecordsJson 字段存储JSON格式的零件更换记录'
PRINT '2. 应用程序通过 PartReplacementRequestCollectionDto 类进行序列化/反序列化'
PRINT '3. 使用 V_RepairOrdersWithParts 视图可以快速查询包含零件记录的维修单'
PRINT '4. JSON 数据结构包含：零件名称、规格、数量、状态等信息'
PRINT ''
PRINT '注意事项：'
PRINT '- JSON 字段不支持传统的外键约束'
PRINT '- 查询 JSON 内容需要使用 OPENJSON 函数'
PRINT '- 建议在应用层进行数据验证'
PRINT '========================================'

-- 显示迁移后的表结构信息
PRINT ''
PRINT '当前 RepairOrders 表结构（新增字段）：'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairOrders' 
AND COLUMN_NAME = 'PartReplacementRecordsJson'
