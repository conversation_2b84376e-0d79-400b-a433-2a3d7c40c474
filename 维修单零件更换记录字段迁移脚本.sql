-- =============================================
-- 维修单零件更换记录表迁移脚本
-- 创建日期: 2025-01-07
-- 版本: 2.0.0
-- 描述: 创建RepairOrderPartRequests表用于存储零件更换记录
-- =============================================

USE [EquipmentManagement]
GO

PRINT '开始执行维修单零件更换记录表迁移...'
PRINT '========================================'

-- =============================================
-- 第一步：检查基础表是否存在
-- =============================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') AND type in (N'U'))
BEGIN
    PRINT '❌ RepairOrders表不存在，请先创建基础表结构'
    RETURN
END

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND type in (N'U'))
BEGIN
    PRINT '❌ Users表不存在，请先创建基础表结构'
    RETURN
END

PRINT '✓ 基础表存在，继续执行迁移'

-- =============================================
-- 第二步：创建零件更换申请表
-- =============================================

PRINT '1. 创建RepairOrderPartRequests表...'

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RepairOrderPartRequests](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RepairOrderId] [int] NOT NULL,                 -- 关联的维修单ID
        [PartName] [nvarchar](100) NOT NULL,            -- 零件名称
        [Specification] [nvarchar](200) NULL,           -- 规格型号
        [RequestedQuantity] [int] NOT NULL DEFAULT(1),  -- 申请数量
        [Unit] [nvarchar](20) NOT NULL DEFAULT('个'),   -- 计量单位
        [Reason] [nvarchar](500) NULL,                  -- 更换原因
        [Remark] [nvarchar](1000) NULL,                 -- 备注
        [Status] [int] NOT NULL DEFAULT(1),             -- 状态：1=申请中,2=已批准,3=已领用,4=已安装,5=已取消
        [RequestedBy] [int] NOT NULL,                   -- 申请人ID
        [RequestedAt] [datetime2] NOT NULL DEFAULT(GETDATE()), -- 申请时间

        -- 外部系统集成预留字段
        [ExternalPartNumber] [nvarchar](50) NULL,       -- 外部系统零件编号
        [ExternalRequisitionDetailId] [nvarchar](50) NULL, -- 外部系统领用单明细ID
        [ActualQuantity] [int] NULL,                    -- 实际领用数量
        [ActualPartName] [nvarchar](100) NULL,          -- 实际领用名称
        [ActualSpecification] [nvarchar](200) NULL,     -- 实际领用规格

        -- 审批和处理信息
        [ApprovedBy] [int] NULL,                        -- 批准人ID
        [ApprovedAt] [datetime2] NULL,                  -- 批准时间
        [IssuedBy] [int] NULL,                          -- 发放人ID
        [IssuedAt] [datetime2] NULL,                    -- 发放时间
        [InstalledBy] [int] NULL,                       -- 安装人ID
        [InstalledAt] [datetime2] NULL,                 -- 安装时间

        -- 成本信息
        [UnitPrice] [decimal](18,2) NULL,               -- 单价
        [TotalCost] [decimal](18,2) NULL,               -- 总成本
        [WarehouseOrderNumber] [nvarchar](50) NULL,     -- 仓库出库单号

        -- 系统字段
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime2] NULL,
        [CreatedBy] [int] NULL,
        [UpdatedBy] [int] NULL,

        CONSTRAINT [PK_RepairOrderPartRequests] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RepairOrderPartRequests_RepairOrders] FOREIGN KEY([RepairOrderId]) REFERENCES [dbo].[RepairOrders] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_RepairOrderPartRequests_RequestedBy] FOREIGN KEY([RequestedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_ApprovedBy] FOREIGN KEY([ApprovedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_IssuedBy] FOREIGN KEY([IssuedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_InstalledBy] FOREIGN KEY([InstalledBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_CreatedBy] FOREIGN KEY([CreatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_UpdatedBy] FOREIGN KEY([UpdatedBy]) REFERENCES [dbo].[Users] ([Id])
    )

    PRINT '✓ RepairOrderPartRequests表创建成功'
END
ELSE
BEGIN
    PRINT '○ RepairOrderPartRequests表已存在'
END

-- =============================================
-- 第三步：创建索引
-- =============================================

PRINT '2. 创建索引...'

-- 主要查询索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_RepairOrderId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RepairOrderId] ON [dbo].[RepairOrderPartRequests] ([RepairOrderId])
    PRINT '✓ 维修单ID索引创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_Status')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_Status] ON [dbo].[RepairOrderPartRequests] ([Status])
    PRINT '✓ 状态索引创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_RequestedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RequestedAt] ON [dbo].[RepairOrderPartRequests] ([RequestedAt])
    PRINT '✓ 申请时间索引创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalPartNumber')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests] ([ExternalPartNumber])
    WHERE [ExternalPartNumber] IS NOT NULL
    PRINT '✓ 外部零件编号索引创建成功'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND name = N'IX_RepairOrderPartRequests_ExternalRequisitionDetailId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests] ([ExternalRequisitionDetailId])
    WHERE [ExternalRequisitionDetailId] IS NOT NULL
    PRINT '✓ 外部领用单明细ID索引创建成功'
END

-- =============================================
-- 第四步：添加表和字段注释
-- =============================================

PRINT '3. 添加表和字段注释...'

-- 添加表注释
IF NOT EXISTS (SELECT * FROM sys.extended_properties WHERE major_id = OBJECT_ID(N'[dbo].[RepairOrderPartRequests]') AND minor_id = 0 AND name = 'MS_Description')
BEGIN
    EXEC sys.sp_addextendedproperty
        @name = N'MS_Description',
        @value = N'维修单零件更换申请表',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'RepairOrderPartRequests'
    PRINT '✓ 表注释添加成功'
END

-- =============================================
-- 第五步：验证迁移结果
-- =============================================

PRINT '5. 验证迁移结果...'

-- 检查字段是否正确添加
IF EXISTS (SELECT * FROM sys.columns 
           WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
           AND name = 'PartReplacementRecordsJson'
           AND system_type_id = (SELECT system_type_id FROM sys.types WHERE name = 'nvarchar')
           AND max_length = -1) -- -1 表示 max
BEGIN
    PRINT '✓ 字段验证成功：PartReplacementRecordsJson (nvarchar(max), NULL)'
END
ELSE
BEGIN
    PRINT '❌ 字段验证失败'
    RETURN
END

-- 检查字段注释
IF EXISTS (SELECT * FROM sys.extended_properties 
           WHERE major_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
           AND minor_id = (SELECT column_id FROM sys.columns 
                          WHERE object_id = OBJECT_ID(N'[dbo].[RepairOrders]') 
                          AND name = 'PartReplacementRecordsJson')
           AND name = 'MS_Description')
BEGIN
    PRINT '✓ 字段注释验证成功'
END
ELSE
BEGIN
    PRINT '⚠ 字段注释验证失败（不影响功能）'
END

-- =============================================
-- 第六步：插入示例数据（可选）
-- =============================================

PRINT '6. 插入示例数据...'

-- 为现有的维修单添加示例零件更换记录（仅用于测试）
-- 在生产环境中，这部分应该注释掉

/*
-- 示例：为第一个维修单添加零件更换记录
IF EXISTS (SELECT TOP 1 * FROM [dbo].[RepairOrders] WHERE PartReplacementRecordsJson IS NULL)
BEGIN
    DECLARE @SampleJson NVARCHAR(MAX) = N'[
        {
            "tempId": "' + CAST(NEWID() AS NVARCHAR(36)) + '",
            "partName": "轴承",
            "specification": "6205-2RS",
            "requestedQuantity": 2,
            "unit": "个",
            "reason": "轴承磨损严重，需要更换",
            "status": 1,
            "createdAt": "' + FORMAT(GETDATE(), 'yyyy-MM-ddTHH:mm:ss.fffZ') + '"
        },
        {
            "tempId": "' + CAST(NEWID() AS NVARCHAR(36)) + '",
            "partName": "密封圈",
            "specification": "O型圈 φ20×2",
            "requestedQuantity": 4,
            "unit": "个",
            "reason": "密封圈老化漏油",
            "status": 1,
            "createdAt": "' + FORMAT(GETDATE(), 'yyyy-MM-ddTHH:mm:ss.fffZ') + '"
        }
    ]'
    
    UPDATE TOP(1) [dbo].[RepairOrders] 
    SET PartReplacementRecordsJson = @SampleJson
    WHERE PartReplacementRecordsJson IS NULL
    
    PRINT '✓ 示例数据插入成功'
END
ELSE
BEGIN
    PRINT '○ 跳过示例数据插入'
END
*/

PRINT '○ 跳过示例数据插入（生产环境）'

-- =============================================
-- 第七步：创建查询视图（可选）
-- =============================================

PRINT '7. 创建查询视图...'

-- 创建一个视图来方便查询包含零件更换记录的维修单
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrdersWithParts]'))
    DROP VIEW [dbo].[V_RepairOrdersWithParts]
GO

CREATE VIEW [dbo].[V_RepairOrdersWithParts]
AS
SELECT 
    ro.Id,
    ro.OrderNumber,
    ro.Title,
    ro.Status,
    ro.CreatedAt,
    ro.UpdatedAt,
    CASE 
        WHEN ro.PartReplacementRecordsJson IS NULL OR ro.PartReplacementRecordsJson = '' 
        THEN 0 
        ELSE 1 
    END AS HasPartReplacementRecords,
    CASE 
        WHEN ro.PartReplacementRecordsJson IS NULL OR ro.PartReplacementRecordsJson = '' 
        THEN 0 
        ELSE (
            SELECT COUNT(*)
            FROM OPENJSON(ro.PartReplacementRecordsJson)
        )
    END AS PartReplacementRecordsCount,
    ro.PartReplacementRecordsJson
FROM [dbo].[RepairOrders] ro
GO

PRINT '✓ 查询视图创建成功'

-- =============================================
-- 第八步：设置权限
-- =============================================

PRINT '8. 设置权限...'

-- 为应用程序用户授权（如果存在）
IF EXISTS (SELECT * FROM sys.database_principals WHERE name = 'CoreHubApp')
BEGIN
    GRANT SELECT, UPDATE ON [dbo].[RepairOrders] TO [CoreHubApp]
    GRANT SELECT ON [dbo].[V_RepairOrdersWithParts] TO [CoreHubApp]
    PRINT '✓ 应用程序用户权限设置完成'
END
ELSE
BEGIN
    PRINT '○ 应用程序用户不存在，跳过权限设置'
END

-- =============================================
-- 完成迁移
-- =============================================

PRINT ''
PRINT '========================================'
PRINT '维修单零件更换记录字段迁移完成！'
PRINT ''
PRINT '迁移内容总结：'
PRINT '- 添加了 PartReplacementRecordsJson 字段到 RepairOrders 表'
PRINT '- 字段类型：nvarchar(max)，允许NULL'
PRINT '- 添加了字段注释'
PRINT '- 创建了查询视图 V_RepairOrdersWithParts'
PRINT '- 设置了相应的权限'
PRINT ''
PRINT '使用说明：'
PRINT '1. PartReplacementRecordsJson 字段存储JSON格式的零件更换记录'
PRINT '2. 应用程序通过 PartReplacementRequestCollectionDto 类进行序列化/反序列化'
PRINT '3. 使用 V_RepairOrdersWithParts 视图可以快速查询包含零件记录的维修单'
PRINT '4. JSON 数据结构包含：零件名称、规格、数量、状态等信息'
PRINT ''
PRINT '注意事项：'
PRINT '- JSON 字段不支持传统的外键约束'
PRINT '- 查询 JSON 内容需要使用 OPENJSON 函数'
PRINT '- 建议在应用层进行数据验证'
PRINT '========================================'

-- 显示迁移后的表结构信息
PRINT ''
PRINT '当前 RepairOrders 表结构（新增字段）：'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'RepairOrders' 
AND COLUMN_NAME = 'PartReplacementRecordsJson'
