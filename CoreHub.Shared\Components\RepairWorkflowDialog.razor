@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Services
@using CoreHub.Shared.Components
@inject IRepairWorkflowService RepairWorkflowService
@inject IRepairOrderService RepairOrderService
@inject IRepairOrderPartRequestService PartRequestService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 800px;">
            <MudText Typo="Typo.h6" Class="mb-4">
                维修工作流操作
            </MudText>

            @if (RepairOrder != null)
            {
                <!-- 报修单信息 -->
                <MudCard Class="mb-4">
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="6">
                                <MudText><strong>报修单号:</strong> @RepairOrder.OrderNumber</MudText>
                            </MudItem>
                            <MudItem xs="6">
                                <MudText><strong>设备名称:</strong> @RepairOrder.EquipmentName</MudText>
                            </MudItem>
                            <MudItem xs="6">
                                <MudText><strong>当前状态:</strong>
                                    <MudChip T="string" Color="@GetStatusColor(RepairOrder.Status)" Size="Size.Small">
                                        @GetStatusName(RepairOrder.Status)
                                    </MudChip>
                                </MudText>
                            </MudItem>
                            <MudItem xs="6">
                                <MudText><strong>紧急程度:</strong>
                                    <MudChip T="string" Color="@GetUrgencyColor(RepairOrder.UrgencyLevel)" Size="Size.Small">
                                        @GetUrgencyName(RepairOrder.UrgencyLevel)
                                    </MudChip>
                                </MudText>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>

                <!-- 标签页 -->
                <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                    <MudTabPanel Text="工作流操作" Icon="@Icons.Material.Filled.PlayArrow">
                        @* 工作流操作内容 *@

                        <!-- 可用操作 -->
                        <MudCard Class="mb-4">
                            <MudCardContent>
                                <MudText Typo="Typo.subtitle1" Class="mb-3">可用操作</MudText>

                                @if (loading)
                                {
                                    <MudProgressLinear Indeterminate="true" />
                                }
                                else if (availableTransitions.Any())
                                {
                                    <MudStack Spacing="2">
                                        @foreach (var transition in availableTransitions)
                                        {
                                            <MudCard Outlined="true" Class="pa-3">
                                                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                                    <div>
                                                        <MudText Typo="Typo.subtitle2">@transition.ActionName</MudText>
                                                        <MudText Typo="Typo.caption">@transition.ActionDescription</MudText>
                                                        @if (!string.IsNullOrEmpty(transition.RequiredRole))
                                                        {
                                                            <MudChip T="string" Color="Color.Secondary" Size="Size.Small" Class="mt-1">
                                                                需要: @transition.RequiredRole
                                                            </MudChip>
                                                        }
                                                    </div>
                                                    <MudButton Variant="Variant.Filled"
                                                             Color="@GetActionColor(transition.ToStatus)"
                                                             Size="Size.Small"
                                                             OnClick="() => SelectAction(transition)">
                                                        执行
                                                    </MudButton>
                                                </MudStack>
                                            </MudCard>
                                        }
                                    </MudStack>
                                }
                                else
                                {
                                    <MudAlert Severity="Severity.Info">
                                        当前状态下没有可用的操作
                                    </MudAlert>
                                }
                            </MudCardContent>
                        </MudCard>

                        <!-- 选中操作的详细信息 -->
                        @if (selectedTransition != null)
                        {
                            <MudCard Class="mb-4">
                                <MudCardContent>
                                    <MudText Typo="Typo.subtitle1" Class="mb-3">
                                        @selectedTransition.ActionName
                                    </MudText>

                                    <!-- 完成维修的特殊表单 -->
                                    @if (selectedTransition.ToStatus == 3) // 已完成
                                    {
                                        <MudStack Spacing="3">
                                            <MudTextField @bind-Value="completeData.RepairDescription"
                                                        Label="维修描述"
                                                        Placeholder="请详细描述维修过程和解决方案..."
                                                        Lines="4"
                                                        Required="true"
                                                        Variant="Variant.Outlined" />

                                            <MudNumericField @bind-Value="completeData.RepairCost"
                                                           Label="维修费用"
                                                           Placeholder="0.00"
                                                           Format="F2"
                                                           Variant="Variant.Outlined"
                                                           Adornment="Adornment.Start"
                                                           AdornmentText="¥" />

                                            <MudTextField @bind-Value="completeData.PartsUsed"
                                                        Label="使用配件"
                                                        Placeholder="列出使用的配件和数量..."
                                                        Lines="2"
                                                        Variant="Variant.Outlined" />

                                            <MudTextField @bind-Value="completeData.TestResult"
                                                        Label="测试结果"
                                                        Placeholder="维修后的测试和验证结果..."
                                                        Lines="2"
                                                        Variant="Variant.Outlined" />

                                            <MudCheckBox T="bool" @bind-Checked="completeData.RequiresApproval"
                                                       Label="需要管理员审批" />
                                        </MudStack>
                                    }

                                    <!-- 通用备注 -->
                                    @if (selectedTransition.RequiresComment || selectedTransition.ToStatus != 3)
                                    {
                                        <MudTextField @bind-Value="actionComment"
                                                    Label="@(selectedTransition.RequiresComment ? "备注 (必填)" : "备注 (可选)")"
                                                    Placeholder="添加操作说明或备注..."
                                                    Lines="3"
                                                    Required="@selectedTransition.RequiresComment"
                                                    Variant="Variant.Outlined"
                                                    Class="@(selectedTransition.ToStatus == 3 ? "mt-3" : "")" />
                                    }
                                </MudCardContent>
                            </MudCard>
                        }

                        <!-- 快捷操作按钮 -->
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.subtitle1" Class="mb-3">快捷操作</MudText>
                                <MudStack Row Spacing="2" Wrap="Wrap.Wrap">
                                    @if (RepairOrder.Status == 1 && RepairOrder.AssignedTo == UserId)
                                    {
                                        <MudButton Variant="Variant.Outlined"
                                                 Color="Color.Success"
                                                 StartIcon="@Icons.Material.Filled.PlayArrow"
                                                 OnClick="() => QuickAction(QuickActionType.StartWork)">
                                            开始维修
                                        </MudButton>
                                    }
                                    @if (RepairOrder.Status == 2 && RepairOrder.AssignedTo == UserId)
                                    {
                                        <MudButton Variant="Variant.Outlined"
                                                 Color="Color.Warning"
                                                 StartIcon="@Icons.Material.Filled.Pause"
                                                 OnClick="() => QuickAction(QuickActionType.Pause)">
                                            暂停维修
                                        </MudButton>
                                    }
                                    @if (RepairOrder.Status == 6 && RepairOrder.AssignedTo == UserId)
                                    {
                                        <MudButton Variant="Variant.Outlined"
                                                 Color="Color.Info"
                                                 StartIcon="@Icons.Material.Filled.PlayArrow"
                                                 OnClick="() => QuickAction(QuickActionType.Resume)">
                                            恢复维修
                                        </MudButton>
                                    }
                                    @if ((RepairOrder.Status == 1 || RepairOrder.Status == 2) && RepairOrder.AssignedTo == UserId)
                                    {
                                        <MudButton Variant="Variant.Outlined"
                                                 Color="Color.Secondary"
                                                 StartIcon="@Icons.Material.Filled.Help"
                                                 OnClick="() => QuickAction(QuickActionType.RequestSupport)">
                                            请求支援
                                        </MudButton>
                                    }
                                </MudStack>
                            </MudCardContent>
                        </MudCard>
                    </MudTabPanel>

                    <MudTabPanel Text="操作历史" Icon="@Icons.Material.Filled.History">
                        <!-- 工作流历史记录 -->
                        @if (loadingHistory)
                        {
                            <MudProgressLinear Indeterminate="true" />
                            <MudText Typo="Typo.body2" Class="mt-2">正在加载历史记录...</MudText>
                        }
                        else if (workflowHistory.Any())
                        {
                            <MudTimeline TimelineOrientation="TimelineOrientation.Vertical">
                                @foreach (var history in workflowHistory)
                                {
                                    <MudTimelineItem>
                                        <ItemOpposite>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                @history.FormattedTime
                                            </MudText>
                                        </ItemOpposite>
                                        <ItemDot>
                                            <MudIcon Icon="@history.ActionIcon" Color="@GetHistoryColor(history)" />
                                        </ItemDot>
                                        <ItemContent>
                                            <MudPaper Class="pa-3" Elevation="2">
                                                <div class="d-flex justify-space-between align-center mb-2">
                                                    <MudText Typo="Typo.subtitle2">@history.Action</MudText>
                                                    @if (history.IsStatusChange)
                                                    {
                                                        <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                                            @history.StatusChangeDescription
                                                        </MudChip>
                                                    }
                                                </div>
                                                @if (!string.IsNullOrEmpty(history.Comment))
                                                {
                                                    <MudText Typo="Typo.body2" Class="mb-2">@history.Comment</MudText>
                                                }
                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                    操作人: @history.UserName | @history.DetailedTime
                                                </MudText>
                                            </MudPaper>
                                        </ItemContent>
                                    </MudTimelineItem>
                                }
                            </MudTimeline>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info">
                                暂无操作历史记录
                            </MudAlert>
                        }
                    </MudTabPanel>

                    <MudTabPanel Text="零件管理" Icon="@Icons.Material.Filled.Build">
                        <!-- 零件更换记录管理 -->
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.subtitle1" Class="mb-3">零件更换记录</MudText>

                                @if (RepairOrder != null)
                                {
                                    <PartReplacementRecordInput PartRecords="partReplacementRecords"
                                                                PartRecordsChanged="OnPartRecordsChanged"
                                                                ReadOnly="false"
                                                                RepairOrderId="@RepairOrder.Id"
                                                                CurrentUserId="@UserId" />
                                }
                            </MudCardContent>
                        </MudCard>
                    </MudTabPanel>
                </MudTabs>
            }
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        @if (selectedTransition != null)
        {
            <MudButton Color="Color.Primary" 
                     Variant="Variant.Filled" 
                     OnClick="ExecuteSelectedAction"
                     Disabled="@(executing || !IsActionValid())">
                @if (executing)
                {
                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                    <MudText Class="ms-2">执行中...</MudText>
                }
                else
                {
                    <MudText>确认执行</MudText>
                }
            </MudButton>
        }
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public RepairOrderDetailDto? RepairOrder { get; set; }
    [Parameter] public int UserId { get; set; }

    private bool loading = true;
    private bool executing = false;
    private bool loadingHistory = true;
    private List<StatusTransitionOption> availableTransitions = new();
    private StatusTransitionOption? selectedTransition;
    private string actionComment = string.Empty;
    private CompleteRepairDto completeData = new();
    private List<WorkflowHistoryDto> workflowHistory = new();

    // 零件更换记录相关
    private PartReplacementRequestCollectionDto partReplacementRecords = new();

    protected override async Task OnInitializedAsync()
    {
        if (RepairOrder != null)
        {
            var loadTransitionsTask = LoadAvailableTransitions();
            var loadHistoryTask = LoadWorkflowHistory();
            var loadPartRecordsTask = LoadPartReplacementRecords();

            await Task.WhenAll(loadTransitionsTask, loadHistoryTask, loadPartRecordsTask);
        }
    }

    private async Task LoadAvailableTransitions()
    {
        loading = true;
        try
        {
            if (RepairOrder == null) return;

            availableTransitions = await RepairWorkflowService.GetAvailableStatusTransitionsAsync(RepairOrder.Id, UserId);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载可用操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task LoadWorkflowHistory()
    {
        loadingHistory = true;
        try
        {
            if (RepairOrder == null) return;

            workflowHistory = await RepairWorkflowService.GetWorkflowHistoryAsync(RepairOrder.Id);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载历史记录失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loadingHistory = false;
        }
    }

    private void SelectAction(StatusTransitionOption transition)
    {
        selectedTransition = transition;
        actionComment = string.Empty;
        
        // 重置完成维修数据
        if (transition.ToStatus == 3)
        {
            completeData = new CompleteRepairDto();
        }
        
        StateHasChanged();
    }

    private async Task ExecuteSelectedAction()
    {
        if (selectedTransition == null || RepairOrder == null) return;

        executing = true;
        try
        {
            (bool IsSuccess, string ErrorMessage) result;

            if (selectedTransition.ToStatus == 3) // 完成维修
            {
                completeData.CompletionComment = actionComment;
                result = await RepairWorkflowService.CompleteRepairWorkAsync(RepairOrder.Id, UserId, completeData);
            }
            else
            {
                result = await RepairWorkflowService.ExecuteStatusTransitionAsync(
                    RepairOrder.Id, 
                    selectedTransition.ToStatus, 
                    UserId, 
                    actionComment);
            }

            if (result.IsSuccess)
            {
                Snackbar.Add("操作执行成功", Severity.Success);
                // 刷新历史记录
                await LoadWorkflowHistory();
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            executing = false;
        }
    }

    private async Task QuickAction(QuickActionType actionType)
    {
        if (RepairOrder == null) return;

        executing = true;
        try
        {
            (bool IsSuccess, string ErrorMessage) result = actionType switch
            {
                QuickActionType.StartWork => await RepairWorkflowService.StartRepairWorkAsync(RepairOrder.Id, UserId),
                QuickActionType.Pause => await RepairWorkflowService.PauseRepairWorkAsync(RepairOrder.Id, UserId, "用户暂停"),
                QuickActionType.Resume => await RepairWorkflowService.ResumeRepairWorkAsync(RepairOrder.Id, UserId),
                QuickActionType.RequestSupport => await RepairWorkflowService.RequestSupportAsync(RepairOrder.Id, UserId, "请求技术支援"),
                _ => (false, "未知操作")
            };

            if (result.IsSuccess)
            {
                Snackbar.Add("操作执行成功", Severity.Success);
                // 刷新历史记录
                await LoadWorkflowHistory();
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            executing = false;
        }
    }

    private bool IsActionValid()
    {
        if (selectedTransition == null) return false;

        if (selectedTransition.RequiresComment && string.IsNullOrWhiteSpace(actionComment))
            return false;

        if (selectedTransition.ToStatus == 3) // 完成维修
        {
            return !string.IsNullOrWhiteSpace(completeData.RepairDescription);
        }

        return true;
    }

    private void Cancel() => MudDialog.Cancel();

    private Color GetStatusColor(int status) => CoreHub.Shared.Utils.RepairOrderStatusHelper.GetStatusColor(status);

    private string GetStatusName(int status) => CoreHub.Shared.Utils.RepairOrderStatusHelper.GetStatusName(status);

    private Color GetUrgencyColor(int urgencyLevel) => urgencyLevel switch
    {
        1 => Color.Error, 2 => Color.Warning, 3 => Color.Info, 4 => Color.Default, _ => Color.Default
    };

    private string GetUrgencyName(int urgencyLevel) => urgencyLevel switch
    {
        1 => "紧急", 2 => "高", 3 => "中", 4 => "低", _ => "未知"
    };

    private Color GetActionColor(int toStatus) => toStatus switch
    {
        2 => Color.Info, 3 => Color.Success, 4 => Color.Error,
        5 => Color.Secondary, 6 => Color.Warning, _ => Color.Primary
    };

    private Color GetHistoryColor(WorkflowHistoryDto history)
    {
        return history.Action.ToLower() switch
        {
            var a when a.Contains("创建") => Color.Primary,
            var a when a.Contains("分配") => Color.Info,
            var a when a.Contains("开始") || a.Contains("接受") => Color.Success,
            var a when a.Contains("暂停") => Color.Warning,
            var a when a.Contains("恢复") => Color.Success,
            var a when a.Contains("完成") => Color.Success,
            var a when a.Contains("关闭") => Color.Secondary,
            var a when a.Contains("作废") || a.Contains("取消") => Color.Error,
            var a when a.Contains("审批") => Color.Primary,
            var a when a.Contains("支援") || a.Contains("帮助") => Color.Warning,
            _ => Color.Default
        };
    }

    public enum QuickActionType
    {
        StartWork,
        Pause,
        Resume,
        RequestSupport
    }

    // 零件更换记录相关方法
    private async Task LoadPartReplacementRecords()
    {
        try
        {
            if (RepairOrder?.Id > 0)
            {
                // 从数据库加载现有的零件更换记录
                var existingRecords = await PartRequestService.GetPartRequestsByRepairOrderIdAsync(RepairOrder.Id);

                // 转换为DTO集合
                partReplacementRecords = new PartReplacementRequestCollectionDto();
                foreach (var record in existingRecords)
                {
                    var dto = PartReplacementRequestDto.FromEntity(record);
                    partReplacementRecords.AddPart(dto);
                }
            }
            else
            {
                // 新维修单，初始化为空集合
                partReplacementRecords = new PartReplacementRequestCollectionDto();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载零件记录失败: {ex.Message}", Severity.Error);
            partReplacementRecords = new PartReplacementRequestCollectionDto();
        }
    }

    private async Task OnPartRecordsChanged(PartReplacementRequestCollectionDto updatedRecords)
    {
        partReplacementRecords = updatedRecords;

        // 立即保存到数据库，提供更好的用户体验
        try
        {
            if (RepairOrder?.Id > 0)
            {
                await SavePartRecordsToDatabase();
                Snackbar.Add("零件记录已保存", Severity.Success);
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存零件记录失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SavePartRecordsToDatabase()
    {
        if (RepairOrder?.Id <= 0) return;

        // 获取当前数据库中的记录
        var existingRecords = await PartRequestService.GetPartRequestsByRepairOrderIdAsync(RepairOrder.Id);
        var existingIds = existingRecords.Select(r => r.Id).ToHashSet();

        // 处理新增和更新的记录
        var recordsToCreate = new List<PartReplacementRequestDto>();
        var recordsToUpdate = new List<RepairOrderPartRequest>();

        foreach (var dto in partReplacementRecords.PartRequests)
        {
            if (dto.Id > 0 && existingIds.Contains(dto.Id))
            {
                // 更新现有记录
                var entity = dto.ToEntity();
                entity.RepairOrderId = RepairOrder.Id;
                recordsToUpdate.Add(entity);
            }
            else
            {
                // 新增记录
                recordsToCreate.Add(dto);
            }
        }

        // 处理删除的记录
        var currentIds = partReplacementRecords.PartRequests.Where(p => p.Id > 0).Select(p => p.Id).ToHashSet();
        var recordsToDelete = existingRecords.Where(r => !currentIds.Contains(r.Id)).ToList();

        // 执行数据库操作
        if (recordsToCreate.Any())
        {
            var (success, error, createdIds) = await PartRequestService.CreatePartRequestsBatchAsync(RepairOrder.Id, recordsToCreate);
            if (!success)
            {
                throw new Exception($"创建零件记录失败: {error}");
            }

            // 更新DTO中的ID
            for (int i = 0; i < recordsToCreate.Count && i < createdIds.Count; i++)
            {
                recordsToCreate[i].Id = createdIds[i];
            }
        }

        foreach (var entity in recordsToUpdate)
        {
            var (success, error) = await PartRequestService.UpdatePartRequestAsync(entity);
            if (!success)
            {
                throw new Exception($"更新零件记录失败: {error}");
            }
        }

        foreach (var entity in recordsToDelete)
        {
            var (success, error) = await PartRequestService.DeletePartRequestAsync(entity.Id);
            if (!success)
            {
                throw new Exception($"删除零件记录失败: {error}");
            }
        }
    }
}
