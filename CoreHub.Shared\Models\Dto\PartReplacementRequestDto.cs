using System.ComponentModel.DataAnnotations;
using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Models.Dto
{
    /// <summary>
    /// 零件更换申请DTO - 用于创建维修单时的零件信息录入
    /// </summary>
    public class PartReplacementRequestDto
    {
        /// <summary>
        /// 记录ID（数据库主键，新建时为0）
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 临时ID（用于前端列表管理）
        /// </summary>
        public string TempId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 维修单ID
        /// </summary>
        public int RepairOrderId { get; set; }

        /// <summary>
        /// 零件名称
        /// </summary>
        [Required(ErrorMessage = "零件名称不能为空")]
        [StringLength(100, ErrorMessage = "零件名称长度不能超过100个字符")]
        public string PartName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200, ErrorMessage = "规格型号长度不能超过200个字符")]
        public string? Specification { get; set; }

        /// <summary>
        /// 申请数量
        /// </summary>
        [Required(ErrorMessage = "申请数量不能为空")]
        [Range(1, 9999, ErrorMessage = "申请数量必须在1-9999之间")]
        public int RequestedQuantity { get; set; } = 1;

        /// <summary>
        /// 计量单位
        /// </summary>
        [StringLength(20, ErrorMessage = "计量单位长度不能超过20个字符")]
        public string Unit { get; set; } = "个";

        /// <summary>
        /// 更换原因
        /// </summary>
        [StringLength(500, ErrorMessage = "更换原因长度不能超过500个字符")]
        public string? Reason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        /// <summary>
        /// 预留字段：外部系统零件编号
        /// </summary>
        public string? ExternalPartNumber { get; set; }

        /// <summary>
        /// 预留字段：外部系统领用单明细ID
        /// </summary>
        public string? ExternalRequisitionDetailId { get; set; }

        /// <summary>
        /// 预留字段：实际领用数量
        /// </summary>
        public int? ActualQuantity { get; set; }

        /// <summary>
        /// 预留字段：实际领用名称
        /// </summary>
        public string? ActualPartName { get; set; }

        /// <summary>
        /// 预留字段：实际领用规格
        /// </summary>
        public string? ActualSpecification { get; set; }

        /// <summary>
        /// 申请人ID
        /// </summary>
        public int RequestedBy { get; set; }

        /// <summary>
        /// 状态（1=申请中,2=已领用,3=已安装,4=已取消）
        /// </summary>
        public int Status { get; set; } = 1;

        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName => Status switch
        {
            1 => "申请中",
            2 => "已领用",
            3 => "已安装",
            4 => "已取消",
            _ => "未知"
        };

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 是否可以编辑
        /// </summary>
        public bool CanEdit => Status == 1; // 只有申请中状态可以编辑

        /// <summary>
        /// 是否可以删除
        /// </summary>
        public bool CanDelete => Status == 1; // 只有申请中状态可以删除

        /// <summary>
        /// 显示名称（用于界面显示）
        /// </summary>
        public string DisplayName
        {
            get
            {
                var name = PartName;
                if (!string.IsNullOrWhiteSpace(Specification))
                {
                    name += $" ({Specification})";
                }
                return name;
            }
        }

        /// <summary>
        /// 数量描述
        /// </summary>
        public string QuantityDescription => $"{RequestedQuantity} {Unit}";

        /// <summary>
        /// 验证数据有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public (bool IsValid, List<string> Errors) Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(PartName))
            {
                errors.Add("零件名称不能为空");
            }
            else if (PartName.Length > 100)
            {
                errors.Add("零件名称长度不能超过100个字符");
            }

            if (!string.IsNullOrWhiteSpace(Specification) && Specification.Length > 200)
            {
                errors.Add("规格型号长度不能超过200个字符");
            }

            if (RequestedQuantity < 1 || RequestedQuantity > 9999)
            {
                errors.Add("申请数量必须在1-9999之间");
            }

            if (string.IsNullOrWhiteSpace(Unit))
            {
                errors.Add("计量单位不能为空");
            }
            else if (Unit.Length > 20)
            {
                errors.Add("计量单位长度不能超过20个字符");
            }

            if (!string.IsNullOrWhiteSpace(Reason) && Reason.Length > 500)
            {
                errors.Add("更换原因长度不能超过500个字符");
            }

            if (!string.IsNullOrWhiteSpace(Remark) && Remark.Length > 1000)
            {
                errors.Add("备注长度不能超过1000个字符");
            }

            return (errors.Count == 0, errors);
        }

        /// <summary>
        /// 创建副本
        /// </summary>
        /// <returns>副本对象</returns>
        public PartReplacementRequestDto Clone()
        {
            return new PartReplacementRequestDto
            {
                TempId = Guid.NewGuid().ToString(), // 生成新的临时ID
                PartName = PartName,
                Specification = Specification,
                RequestedQuantity = RequestedQuantity,
                Unit = Unit,
                Reason = Reason,
                Remark = Remark,
                ExternalPartNumber = ExternalPartNumber,
                ExternalRequisitionDetailId = ExternalRequisitionDetailId,
                ActualQuantity = ActualQuantity,
                ActualPartName = ActualPartName,
                ActualSpecification = ActualSpecification,
                Status = Status,
                CreatedAt = DateTime.Now,
                UpdatedAt = UpdatedAt
            };
        }

        /// <summary>
        /// 重置为默认值
        /// </summary>
        public void Reset()
        {
            Id = 0;
            TempId = Guid.NewGuid().ToString();
            RepairOrderId = 0;
            PartName = string.Empty;
            Specification = null;
            RequestedQuantity = 1;
            Unit = "个";
            Reason = null;
            Remark = null;
            RequestedBy = 0;
            ExternalPartNumber = null;
            ExternalRequisitionDetailId = null;
            ActualQuantity = null;
            ActualPartName = null;
            ActualSpecification = null;
            Status = 1;
            CreatedAt = DateTime.Now;
            UpdatedAt = null;
        }

        /// <summary>
        /// 从实体转换为DTO
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>DTO对象</returns>
        public static PartReplacementRequestDto FromEntity(RepairOrderPartRequest entity)
        {
            return new PartReplacementRequestDto
            {
                Id = entity.Id,
                TempId = entity.Id > 0 ? entity.Id.ToString() : Guid.NewGuid().ToString(),
                RepairOrderId = entity.RepairOrderId,
                PartName = entity.PartName,
                Specification = entity.Specification,
                RequestedQuantity = entity.RequestedQuantity,
                Unit = entity.Unit,
                Reason = entity.Reason,
                Remark = entity.Remark,
                RequestedBy = entity.RequestedBy,
                ExternalPartNumber = entity.ExternalPartNumber,
                ExternalRequisitionDetailId = entity.ExternalRequisitionDetailId,
                ActualQuantity = entity.ActualQuantity,
                ActualPartName = entity.ActualPartName,
                ActualSpecification = entity.ActualSpecification,
                Status = entity.Status,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt
            };
        }

        /// <summary>
        /// 转换为实体对象
        /// </summary>
        /// <returns>实体对象</returns>
        public RepairOrderPartRequest ToEntity()
        {
            return new RepairOrderPartRequest
            {
                Id = Id,
                RepairOrderId = RepairOrderId,
                PartName = PartName,
                Specification = Specification,
                RequestedQuantity = RequestedQuantity,
                Unit = Unit,
                Reason = Reason,
                Remark = Remark,
                RequestedBy = RequestedBy,
                ExternalPartNumber = ExternalPartNumber,
                ExternalRequisitionDetailId = ExternalRequisitionDetailId,
                ActualQuantity = ActualQuantity,
                ActualPartName = ActualPartName,
                ActualSpecification = ActualSpecification,
                Status = Status,
                CreatedAt = CreatedAt,
                UpdatedAt = UpdatedAt
            };
        }
    }

    /// <summary>
    /// 零件更换记录集合DTO
    /// </summary>
    public class PartReplacementRequestCollectionDto
    {
        /// <summary>
        /// 零件更换记录列表
        /// </summary>
        public List<PartReplacementRequestDto> PartRequests { get; set; } = new List<PartReplacementRequestDto>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount => PartRequests.Count;

        /// <summary>
        /// 是否有记录
        /// </summary>
        public bool HasAnyParts => PartRequests.Any();

        /// <summary>
        /// 申请中的记录数
        /// </summary>
        public int PendingCount => PartRequests.Count(p => p.Status == 1);

        /// <summary>
        /// 已完成的记录数
        /// </summary>
        public int CompletedCount => PartRequests.Count(p => p.Status == 3);

        /// <summary>
        /// 总申请数量
        /// </summary>
        public int TotalRequestedQuantity => PartRequests.Sum(p => p.RequestedQuantity);

        /// <summary>
        /// 添加零件记录
        /// </summary>
        /// <param name="partRequest">零件记录</param>
        public void AddPart(PartReplacementRequestDto partRequest)
        {
            if (partRequest != null)
            {
                PartRequests.Add(partRequest);
            }
        }

        /// <summary>
        /// 移除零件记录
        /// </summary>
        /// <param name="tempId">临时ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemovePart(string tempId)
        {
            var part = PartRequests.FirstOrDefault(p => p.TempId == tempId);
            if (part != null)
            {
                return PartRequests.Remove(part);
            }
            return false;
        }

        /// <summary>
        /// 获取零件记录
        /// </summary>
        /// <param name="tempId">临时ID</param>
        /// <returns>零件记录</returns>
        public PartReplacementRequestDto? GetPart(string tempId)
        {
            return PartRequests.FirstOrDefault(p => p.TempId == tempId);
        }

        /// <summary>
        /// 更新零件记录
        /// </summary>
        /// <param name="updatedPart">更新的零件记录</param>
        /// <returns>是否成功更新</returns>
        public bool UpdatePart(PartReplacementRequestDto updatedPart)
        {
            var index = PartRequests.FindIndex(p => p.TempId == updatedPart.TempId);
            if (index >= 0)
            {
                updatedPart.UpdatedAt = DateTime.Now;
                PartRequests[index] = updatedPart;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 清空所有记录
        /// </summary>
        public void Clear()
        {
            PartRequests.Clear();
        }

        /// <summary>
        /// 验证所有记录
        /// </summary>
        /// <returns>验证结果</returns>
        public (bool IsValid, Dictionary<string, List<string>> Errors) ValidateAll()
        {
            var allErrors = new Dictionary<string, List<string>>();
            var isAllValid = true;

            foreach (var part in PartRequests)
            {
                var (isValid, errors) = part.Validate();
                if (!isValid)
                {
                    allErrors[part.TempId] = errors;
                    isAllValid = false;
                }
            }

            return (isAllValid, allErrors);
        }

        /// <summary>
        /// 从实体列表创建DTO集合
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>DTO集合</returns>
        public static PartReplacementRequestCollectionDto FromEntities(List<RepairOrderPartRequest> entities)
        {
            var collection = new PartReplacementRequestCollectionDto();
            foreach (var entity in entities)
            {
                collection.AddPart(PartReplacementRequestDto.FromEntity(entity));
            }
            return collection;
        }

        /// <summary>
        /// 转换为实体列表
        /// </summary>
        /// <returns>实体列表</returns>
        public List<RepairOrderPartRequest> ToEntities()
        {
            return PartRequests.Select(dto => dto.ToEntity()).ToList();
        }

        /// <summary>
        /// 获取新增的记录（ID为0的记录）
        /// </summary>
        /// <returns>新增记录列表</returns>
        public List<PartReplacementRequestDto> GetNewRecords()
        {
            return PartRequests.Where(p => p.Id == 0).ToList();
        }

        /// <summary>
        /// 获取已存在的记录（ID大于0的记录）
        /// </summary>
        /// <returns>已存在记录列表</returns>
        public List<PartReplacementRequestDto> GetExistingRecords()
        {
            return PartRequests.Where(p => p.Id > 0).ToList();
        }
    }
}
