using SqlSugar;
using System.ComponentModel.DataAnnotations;
using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 报修单实体
    /// </summary>
    [SugarTable("RepairOrders")]
    public class RepairOrder
    {
        /// <summary>
        /// 报修单ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 报修单号（唯一）
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false)]
        [Required(ErrorMessage = "报修单号不能为空")]
        [StringLength(50, ErrorMessage = "报修单号长度不能超过50个字符")]
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 设备ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "设备不能为空")]
        public int EquipmentId { get; set; }

        /// <summary>
        /// 报修人ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "报修人不能为空")]
        public int ReporterId { get; set; }

        /// <summary>
        /// 故障描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = false)]
        [Required(ErrorMessage = "故障描述不能为空")]
        [StringLength(1000, ErrorMessage = "故障描述长度不能超过1000个字符")]
        public string FaultDescription { get; set; } = string.Empty;

        /// <summary>
        /// 紧急程度（1=紧急,2=高,3=中,4=低）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int UrgencyLevel { get; set; } = 2;

        /// <summary>
        /// 维修部门ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "维修部门不能为空")]
        public int MaintenanceDepartmentId { get; set; }

        /// <summary>
        /// 状态（1=待处理,2=处理中,3=已完成,4=已作废,5=已关闭,6=已暂停,7=待审批,8=待确认）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 指派给（维修人员ID）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? AssignedTo { get; set; }

        /// <summary>
        /// 报修时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime ReportedAt { get; set; }

        /// <summary>
        /// 指派时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? AssignedAt { get; set; }

        /// <summary>
        /// 开始维修时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 作废时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? CancelledAt { get; set; }

        /// <summary>
        /// 作废原因
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "作废原因长度不能超过500个字符")]
        public string? CancelReason { get; set; }

        /// <summary>
        /// 维修描述
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "维修描述长度不能超过1000个字符")]
        public string? RepairDescription { get; set; }

        /// <summary>
        /// 维修费用
        /// </summary>
        [SugarColumn(IsNullable = true, DecimalDigits = 2)]
        public decimal? RepairCost { get; set; }

        /// <summary>
        /// 使用配件
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "使用配件长度不能超过1000个字符")]
        public string? PartsUsed { get; set; }



        /// <summary>
        /// 测试结果
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "测试结果长度不能超过500个字符")]
        public string? TestResult { get; set; }

        /// <summary>
        /// 报修人评价（1-5星）
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ReporterRating { get; set; }

        /// <summary>
        /// 报修人评价意见
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "评价意见长度不能超过500个字符")]
        public string? ReporterComment { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        // 导航属性
        /// <summary>
        /// 设备
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Equipment? Equipment { get; set; }

        /// <summary>
        /// 维修部门
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public Department? MaintenanceDepartment { get; set; }

        /// <summary>
        /// 报修单附件
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<RepairOrderAttachment> Attachments { get; set; } = new List<RepairOrderAttachment>();

        /// <summary>
        /// 零件更换申请记录
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<RepairOrderPartRequest> PartRequests { get; set; } = new List<RepairOrderPartRequest>();

        /// <summary>
        /// 紧急程度名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string UrgencyLevelName => UrgencyLevel switch
        {
            1 => "紧急",
            2 => "高",
            3 => "中",
            4 => "低",
            _ => "未知"
        };

        /// <summary>
        /// 状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => CoreHub.Shared.Utils.RepairOrderStatusHelper.GetStatusName(Status);

        /// <summary>
        /// 是否有零件更换申请
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool HasPartRequests => PartRequests.Any();

        /// <summary>
        /// 零件申请总数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int PartRequestsCount => PartRequests.Count;

        /// <summary>
        /// 申请中的零件记录数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int PendingPartRequestsCount => PartRequests.Count(p => p.Status == 1);

        /// <summary>
        /// 已完成的零件记录数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int CompletedPartRequestsCount => PartRequests.Count(p => p.Status == 3);

        /// <summary>
        /// 已取消的零件记录数
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int CancelledPartRequestsCount => PartRequests.Count(p => p.Status == 4);

        /// <summary>
        /// 零件更换进度百分比
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal PartReplacementProgress
        {
            get
            {
                if (!HasPartRequests) return 100;
                var totalRecords = PartRequestsCount;
                var completedRecords = CompletedPartRequestsCount;
                return totalRecords > 0 ? (decimal)completedRecords / totalRecords * 100 : 0;
            }
        }

        /// <summary>
        /// 零件总成本
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public decimal TotalPartCost => PartRequests
            .Where(p => p.Status == CoreHub.Shared.Utils.PartRequestStatusHelper.Installed && p.TotalCost.HasValue)
            .Sum(p => p.TotalCost!.Value);

        /// <summary>
        /// 申请的零件总数量
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int TotalRequestedQuantity => PartRequests.Sum(p => p.RequestedQuantity);

        /// <summary>
        /// 实际领用的零件总数量
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int TotalActualQuantity => PartRequests
            .Where(p => p.ActualQuantity.HasValue)
            .Sum(p => p.ActualQuantity!.Value);
    }
}
