@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using FluentValidation
@inject IEquipmentService EquipmentService
@inject IDepartmentService DepartmentService
@inject IEquipmentModelService EquipmentModelService
@inject ILocationService LocationService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@Equipment" Validation="@(new EquipmentValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Equipment.Code"
                                For="@(() => Equipment.Code)"
                                Label="设备编码"
                                Required="true"
                                Immediate="true"
                                Disabled="@IsEdit" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Equipment.Name"
                                For="@(() => Equipment.Name)"
                                Label="设备名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="Equipment.DepartmentId"
                             For="@(() => Equipment.DepartmentId)"
                             Label="所属部门"
                             Required="true"
                             OnSelectionChanged="OnDepartmentChanged">
                        @foreach (var dept in departments)
                        {
                            <MudSelectItem T="int" Value="@dept.Id">@dept.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="Equipment.ModelId"
                             For="@(() => Equipment.ModelId)"
                             Label="设备型号"
                             Required="true">
                        @foreach (var model in equipmentModels)
                        {
                            <MudSelectItem T="int" Value="@model.Id">@model.Name (@model.Category)</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="Equipment.LocationId"
                             For="@(() => Equipment.LocationId)"
                             Label="所在位置"
                             Required="true">
                        @foreach (var location in filteredLocations)
                        {
                            <MudSelectItem T="int" Value="@location.Id">@location.Name</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Equipment.SerialNumber"
                                For="@(() => Equipment.SerialNumber)"
                                Label="序列号"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="Equipment.AssetNumber"
                                For="@(() => Equipment.AssetNumber)"
                                Label="资产编号"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSelect T="int" @bind-Value="Equipment.Status"
                             For="@(() => Equipment.Status)"
                             Label="设备状态">
                        <MudSelectItem T="int" Value="1">正常</MudSelectItem>
                        <MudSelectItem T="int" Value="2">维修中</MudSelectItem>
                        <MudSelectItem T="int" Value="3">停用</MudSelectItem>
                        <MudSelectItem T="int" Value="4">报废</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="Equipment.PurchaseDate"
                                 Label="购买日期"
                                 Clearable="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="Equipment.WarrantyExpiry"
                                 Label="保修到期日期"
                                 Clearable="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="Equipment.LastMaintenanceDate"
                                 Label="最后维护日期"
                                 Clearable="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudDatePicker @bind-Date="Equipment.NextMaintenanceDate"
                                 Label="下次维护日期"
                                 Clearable="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Equipment.Description"
                                For="@(() => Equipment.Description)"
                                Label="设备描述"
                                Lines="3"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudSwitch T="bool" @bind-Checked="Equipment.IsEnabled"
                             Label="@($"启用状态 (当前值: {Equipment.IsEnabled})")"
                             Color="Color.Primary" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="Equipment.Remark"
                                For="@(() => Equipment.Remark)"
                                Label="备注"
                                Lines="2"
                                Immediate="true" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@saving">
            @if (saving)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public Equipment Equipment { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; } = false;

    private MudForm form = null!;
    private bool saving = false;
    private List<Department> departments = new();
    private List<EquipmentModel> equipmentModels = new();
    private List<Location> locations = new();
    private List<Location> filteredLocations = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadBasicData();

        if (!IsEdit)
        {
            Equipment.Status = 1;
            Equipment.IsEnabled = true;
        }
        else
        {
            // 编辑模式下，根据当前部门过滤位置
            FilterLocationsByDepartment();

            // 调试信息
            Console.WriteLine($"[EquipmentEditDialog] 编辑设备 {Equipment.Id}:");
            Console.WriteLine($"[EquipmentEditDialog] - 设备编码: {Equipment.Code}");
            Console.WriteLine($"[EquipmentEditDialog] - 设备名称: {Equipment.Name}");
            Console.WriteLine($"[EquipmentEditDialog] - 设备状态 (Status): {Equipment.Status}");
            Console.WriteLine($"[EquipmentEditDialog] - 启用状态 (IsEnabled): {Equipment.IsEnabled}");
        }
    }

    private async Task LoadBasicData()
    {
        try
        {
            var departmentTask = DepartmentService.GetEnabledDepartmentsAsync();
            var equipmentModelTask = EquipmentModelService.GetEnabledEquipmentModelsAsync();
            var locationTask = LocationService.GetEnabledLocationsAsync();

            await Task.WhenAll(departmentTask, equipmentModelTask, locationTask);

            departments = await departmentTask;
            equipmentModels = await equipmentModelTask;
            locations = await locationTask;
            filteredLocations = locations.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载基础数据失败: {ex.Message}", Severity.Error);
        }
    }

    private void OnDepartmentChanged(int departmentId)
    {
        Equipment.DepartmentId = departmentId;
        Equipment.LocationId = 0; // 重置位置选择
        FilterLocationsByDepartment();
    }

    private void FilterLocationsByDepartment()
    {
        if (Equipment.DepartmentId > 0)
        {
            filteredLocations = locations.Where(l => l.DepartmentId == Equipment.DepartmentId).ToList();
        }
        else
        {
            filteredLocations = locations.ToList();
        }
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        await form.Validate();
        if (!form.IsValid) return;

        saving = true;
        try
        {
            (bool IsSuccess, string ErrorMessage) result;
            
            if (IsEdit)
            {
                Equipment.UpdatedAt = DateTime.Now;
                result = await EquipmentService.UpdateEquipmentAsync(Equipment);
            }
            else
            {
                result = await EquipmentService.CreateEquipmentAsync(Equipment);
            }

            if (result.IsSuccess)
            {
                Snackbar.Add($"设备{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    public class EquipmentValidator : AbstractValidator<Equipment>
    {
        public EquipmentValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("设备编码不能为空")
                .MaximumLength(50).WithMessage("设备编码长度不能超过50个字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("设备名称不能为空")
                .MaximumLength(100).WithMessage("设备名称长度不能超过100个字符");

            RuleFor(x => x.DepartmentId)
                .GreaterThan(0).WithMessage("请选择所属部门");

            RuleFor(x => x.ModelId)
                .GreaterThan(0).WithMessage("请选择设备型号");

            RuleFor(x => x.LocationId)
                .GreaterThan(0).WithMessage("请选择所在位置");

            RuleFor(x => x.SerialNumber)
                .MaximumLength(100).WithMessage("序列号长度不能超过100个字符");

            RuleFor(x => x.AssetNumber)
                .MaximumLength(100).WithMessage("资产编号长度不能超过100个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("设备描述长度不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<Equipment>.CreateWithOptions((Equipment)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
