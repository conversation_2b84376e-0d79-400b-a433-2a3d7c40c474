@page "/part-replacement-test"
@using CoreHub.Shared.Models.Dto
@using CoreHub.Shared.Components

<PageTitle>零件更换记录测试页面</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">
        <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
        零件更换记录功能测试
    </MudText>

    <MudGrid>
        <!-- 录入组件测试 -->
        <MudItem xs="12" lg="8">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">录入组件测试</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <PartReplacementRecordInput PartRecords="testPartRecords" 
                                                PartRecordsChanged="OnPartRecordsChanged"
                                                ReadOnly="false" />
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 控制面板 -->
        <MudItem xs="12" lg="4">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">控制面板</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <!-- 快速添加示例数据 -->
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Primary" 
                                   FullWidth="true"
                                   OnClick="AddSampleData"
                                   StartIcon="@Icons.Material.Filled.Add">
                            添加示例数据
                        </MudButton>

                        <!-- 清空数据 -->
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Warning" 
                                   FullWidth="true"
                                   OnClick="ClearData"
                                   StartIcon="@Icons.Material.Filled.Clear">
                            清空数据
                        </MudButton>

                        <!-- 验证数据 -->
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Info" 
                                   FullWidth="true"
                                   OnClick="ValidateData"
                                   StartIcon="@Icons.Material.Filled.CheckCircle">
                            验证数据
                        </MudButton>

                        <!-- 导出JSON -->
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Success" 
                                   FullWidth="true"
                                   OnClick="ExportJson"
                                   StartIcon="@Icons.Material.Filled.Download">
                            导出JSON
                        </MudButton>

                        <!-- 导入JSON -->
                        <MudFileUpload T="IBrowserFile" 
                                       Accept=".json"
                                       FilesChanged="ImportJson"
                                       MaximumFileCount="1">
                            <ButtonTemplate>
                                <MudButton HtmlTag="label"
                                           Variant="Variant.Outlined"
                                           Color="Color.Secondary"
                                           StartIcon="@Icons.Material.Filled.Upload"
                                           FullWidth="true"
                                           for="@context">
                                    导入JSON
                                </MudButton>
                            </ButtonTemplate>
                        </MudFileUpload>

                        <!-- 统计信息 -->
                        <MudDivider />
                        <MudText Typo="Typo.subtitle2">统计信息</MudText>
                        <MudSimpleTable Dense="true">
                            <tbody>
                                <tr>
                                    <td>总记录数</td>
                                    <td>@testPartRecords.TotalCount</td>
                                </tr>
                                <tr>
                                    <td>申请中</td>
                                    <td>@testPartRecords.PendingCount</td>
                                </tr>
                                <tr>
                                    <td>已完成</td>
                                    <td>@testPartRecords.CompletedCount</td>
                                </tr>
                                <tr>
                                    <td>总数量</td>
                                    <td>@testPartRecords.TotalRequestedQuantity</td>
                                </tr>
                            </tbody>
                        </MudSimpleTable>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- 显示组件测试 -->
        <MudItem xs="12">
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">显示组件测试</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudStack Row Spacing="2">
                            <MudSwitch @bind-Checked="showSummary" Label="显示统计" Color="Color.Primary" />
                            <MudSwitch @bind-Checked="showProgress" Label="显示进度" Color="Color.Primary" />
                            <MudSwitch @bind-Checked="showAsTable" Label="表格模式" Color="Color.Primary" />
                            <MudSwitch @bind-Checked="showDetails" Label="显示详情" Color="Color.Primary" />
                            <MudSwitch @bind-Checked="showExternalFields" Label="外部字段" Color="Color.Primary" />
                        </MudStack>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <PartReplacementRecordDisplay PartRecords="testPartRecords"
                                                  ShowSummary="showSummary"
                                                  ShowProgress="showProgress"
                                                  ShowAsTable="showAsTable"
                                                  ShowDetails="showDetails"
                                                  ShowExternalFields="showExternalFields"
                                                  Dense="true" />
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- JSON数据预览 -->
        <MudItem xs="12">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">JSON数据预览</MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudIconButton Icon="@Icons.Material.Filled.ContentCopy"
                                       OnClick="CopyJsonToClipboard"
                                       Title="复制到剪贴板" />
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudTextField Value="@jsonPreview"
                                  Lines="10"
                                  Variant="Variant.Outlined"
                                  ReadOnly="true"
                                  FullWidth="true"
                                  Class="mud-input-code" />
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

<!-- 验证结果对话框 -->
<MudDialog @bind-IsVisible="showValidationDialog" Options="validationDialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="mr-2" />
            数据验证结果
        </MudText>
    </TitleContent>
    <DialogContent>
        @if (validationResult.IsValid)
        {
            <MudAlert Severity="Severity.Success">
                <MudText>所有数据验证通过！</MudText>
            </MudAlert>
        }
        else
        {
            <MudAlert Severity="Severity.Error" Class="mb-4">
                <MudText>发现 @validationResult.Errors.Count 个记录有错误</MudText>
            </MudAlert>
            
            @foreach (var error in validationResult.Errors)
            {
                <MudExpansionPanels Elevation="1" Class="mb-2">
                    <MudExpansionPanel Text="@($"记录 {error.Key}")">
                        <MudList Dense="true">
                            @foreach (var errorMsg in error.Value)
                            {
                                <MudListItem>
                                    <MudText Color="Color.Error">• @errorMsg</MudText>
                                </MudListItem>
                            }
                        </MudList>
                    </MudExpansionPanel>
                </MudExpansionPanels>
            }
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="() => showValidationDialog = false">关闭</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private PartReplacementRequestCollectionDto testPartRecords = new();
    private string jsonPreview = "{}";
    
    // 显示组件参数
    private bool showSummary = true;
    private bool showProgress = true;
    private bool showAsTable = true;
    private bool showDetails = true;
    private bool showExternalFields = false;
    
    // 验证对话框
    private bool showValidationDialog = false;
    private (bool IsValid, Dictionary<string, List<string>> Errors) validationResult;
    private DialogOptions validationDialogOptions = new()
    {
        MaxWidth = MaxWidth.Medium,
        FullWidth = true
    };

    protected override void OnInitialized()
    {
        UpdateJsonPreview();
    }

    private void OnPartRecordsChanged(PartReplacementRequestCollectionDto updatedRecords)
    {
        testPartRecords = updatedRecords;
        UpdateJsonPreview();
        StateHasChanged();
    }

    private void UpdateJsonPreview()
    {
        try
        {
            jsonPreview = testPartRecords.ToJson();
            if (string.IsNullOrWhiteSpace(jsonPreview))
            {
                jsonPreview = "[]";
            }
            else
            {
                // 格式化JSON以便阅读
                var jsonDocument = System.Text.Json.JsonDocument.Parse(jsonPreview);
                jsonPreview = System.Text.Json.JsonSerializer.Serialize(jsonDocument, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                });
            }
        }
        catch
        {
            jsonPreview = "JSON格式错误";
        }
    }

    private void AddSampleData()
    {
        var sampleParts = new[]
        {
            new PartReplacementRequestDto
            {
                PartName = "轴承",
                Specification = "6205-2RS",
                RequestedQuantity = 2,
                Unit = "个",
                Reason = "轴承磨损严重，需要更换",
                Status = 1
            },
            new PartReplacementRequestDto
            {
                PartName = "密封圈",
                Specification = "O型圈 φ20×2",
                RequestedQuantity = 4,
                Unit = "个",
                Reason = "密封圈老化漏油",
                Status = 2
            },
            new PartReplacementRequestDto
            {
                PartName = "螺栓",
                Specification = "M8×25",
                RequestedQuantity = 8,
                Unit = "个",
                Reason = "螺栓松动，需要更换",
                Status = 4
            }
        };

        foreach (var part in sampleParts)
        {
            testPartRecords.AddPart(part);
        }

        UpdateJsonPreview();
        StateHasChanged();
    }

    private void ClearData()
    {
        testPartRecords.Clear();
        UpdateJsonPreview();
        StateHasChanged();
    }

    private void ValidateData()
    {
        validationResult = testPartRecords.ValidateAll();
        showValidationDialog = true;
    }

    private async Task ExportJson()
    {
        var json = testPartRecords.ToJson();
        var fileName = $"part-replacement-records-{DateTime.Now:yyyyMMdd-HHmmss}.json";
        
        // 这里需要实现文件下载功能
        // 可以使用 IJSRuntime 调用 JavaScript 下载文件
        await Task.CompletedTask; // 占位符
    }

    private async Task ImportJson(IBrowserFile file)
    {
        try
        {
            if (file != null && file.Size > 0)
            {
                using var stream = file.OpenReadStream();
                using var reader = new StreamReader(stream);
                var json = await reader.ReadToEndAsync();
                
                var imported = PartReplacementRequestCollectionDto.FromJson(json);
                testPartRecords = imported;
                UpdateJsonPreview();
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            // 这里应该显示错误消息
            Console.WriteLine($"导入失败: {ex.Message}");
        }
    }

    private async Task CopyJsonToClipboard()
    {
        // 这里需要实现复制到剪贴板功能
        // 可以使用 IJSRuntime 调用 JavaScript
        await Task.CompletedTask; // 占位符
    }
}
