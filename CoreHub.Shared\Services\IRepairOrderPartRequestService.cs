using CoreHub.Shared.Models.Database;
using CoreHub.Shared.Models.Dto;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 维修单零件申请服务接口
    /// </summary>
    public interface IRepairOrderPartRequestService
    {
        #region 基础CRUD操作

        /// <summary>
        /// 获取维修单的所有零件申请
        /// </summary>
        /// <param name="repairOrderId">维修单ID</param>
        /// <returns>零件申请列表</returns>
        Task<List<RepairOrderPartRequest>> GetPartRequestsByRepairOrderIdAsync(int repairOrderId);

        /// <summary>
        /// 根据ID获取零件申请
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns>零件申请</returns>
        Task<RepairOrderPartRequest?> GetPartRequestByIdAsync(int id);

        /// <summary>
        /// 创建零件申请
        /// </summary>
        /// <param name="partRequest">零件申请</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, int? Id)> CreatePartRequestAsync(RepairOrderPartRequest partRequest);

        /// <summary>
        /// 批量创建零件申请
        /// </summary>
        /// <param name="repairOrderId">维修单ID</param>
        /// <param name="partRequests">零件申请列表</param>
        /// <returns>创建结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, List<int> CreatedIds)> CreatePartRequestsBatchAsync(int repairOrderId, List<PartReplacementRequestDto> partRequests);

        /// <summary>
        /// 更新零件申请
        /// </summary>
        /// <param name="partRequest">零件申请</param>
        /// <returns>更新结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdatePartRequestAsync(RepairOrderPartRequest partRequest);

        /// <summary>
        /// 删除零件申请
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns>删除结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> DeletePartRequestAsync(int id);

        #endregion

        #region 状态管理

        /// <summary>
        /// 批准零件申请
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="approverId">批准人ID</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ApprovePartRequestAsync(int id, int approverId);

        /// <summary>
        /// 拒绝零件申请
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="approverId">批准人ID</param>
        /// <param name="reason">拒绝原因</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> RejectPartRequestAsync(int id, int approverId, string reason);

        /// <summary>
        /// 确认零件发放
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="issuerId">发放人ID</param>
        /// <param name="warehouseOrderNumber">仓库出库单号</param>
        /// <param name="unitPrice">实际单价</param>
        /// <param name="totalCost">总成本</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ConfirmPartIssueAsync(int id, int issuerId, string warehouseOrderNumber, decimal? unitPrice = null, decimal? totalCost = null);

        /// <summary>
        /// 确认零件安装
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="installerId">安装人ID</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> ConfirmPartInstallationAsync(int id, int installerId);

        /// <summary>
        /// 取消零件申请
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="operatorId">操作人ID</param>
        /// <param name="reason">取消原因</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> CancelPartRequestAsync(int id, int operatorId, string reason);

        #endregion

        #region 外部系统集成

        /// <summary>
        /// 根据外部零件编号获取申请
        /// </summary>
        /// <param name="externalPartNumber">外部零件编号</param>
        /// <returns>零件申请</returns>
        Task<RepairOrderPartRequest?> GetPartRequestByExternalPartNumberAsync(string externalPartNumber);

        /// <summary>
        /// 根据外部领用单明细ID获取申请
        /// </summary>
        /// <param name="externalRequisitionDetailId">外部领用单明细ID</param>
        /// <returns>零件申请</returns>
        Task<RepairOrderPartRequest?> GetPartRequestByExternalRequisitionDetailIdAsync(string externalRequisitionDetailId);

        /// <summary>
        /// 外部系统回写零件信息
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="externalPartNumber">外部零件编号</param>
        /// <param name="externalRequisitionDetailId">外部领用单明细ID</param>
        /// <param name="actualQuantity">实际数量</param>
        /// <param name="actualPartName">实际零件名称</param>
        /// <param name="actualSpecification">实际规格</param>
        /// <param name="status">状态</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage)> UpdateFromExternalSystemAsync(
            int id,
            string? externalPartNumber = null,
            string? externalRequisitionDetailId = null,
            int? actualQuantity = null,
            string? actualPartName = null,
            string? actualSpecification = null,
            int? status = null);

        /// <summary>
        /// 批量外部系统回写
        /// </summary>
        /// <param name="updates">更新列表</param>
        /// <returns>操作结果</returns>
        Task<(bool IsSuccess, string ErrorMessage, List<int> UpdatedIds)> BatchUpdateFromExternalSystemAsync(List<ExternalPartUpdateDto> updates);

        #endregion

        #region 查询和统计

        /// <summary>
        /// 获取待处理的零件申请
        /// </summary>
        /// <param name="status">状态筛选</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        Task<(List<RepairOrderPartRequest> Items, int TotalCount)> GetPendingPartRequestsAsync(int? status = null, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 获取用户的零件申请
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        Task<(List<RepairOrderPartRequest> Items, int TotalCount)> GetUserPartRequestsAsync(int userId, int pageIndex = 1, int pageSize = 20);

        /// <summary>
        /// 搜索零件申请
        /// </summary>
        /// <param name="keyword">关键词</param>
        /// <param name="status">状态筛选</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>分页结果</returns>
        Task<(List<RepairOrderPartRequest> Items, int TotalCount)> SearchPartRequestsAsync(
            string? keyword = null,
            int? status = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageIndex = 1,
            int pageSize = 20);

        /// <summary>
        /// 获取零件使用统计
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>统计结果</returns>
        Task<List<PartUsageStatisticsDto>> GetPartUsageStatisticsAsync(DateTime startDate, DateTime endDate);

        #endregion

        #region 验证

        /// <summary>
        /// 验证零件申请是否可以编辑
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns>验证结果</returns>
        Task<(bool CanEdit, string Reason)> CanEditPartRequestAsync(int id);

        /// <summary>
        /// 验证零件申请是否可以删除
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns>验证结果</returns>
        Task<(bool CanDelete, string Reason)> CanDeletePartRequestAsync(int id);

        /// <summary>
        /// 验证状态变更是否合法
        /// </summary>
        /// <param name="currentStatus">当前状态</param>
        /// <param name="newStatus">新状态</param>
        /// <returns>是否合法</returns>
        bool IsValidStatusTransition(int currentStatus, int newStatus);

        #endregion
    }

    /// <summary>
    /// 外部系统更新DTO
    /// </summary>
    public class ExternalPartUpdateDto
    {
        /// <summary>
        /// 申请ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 外部零件编号
        /// </summary>
        public string? ExternalPartNumber { get; set; }

        /// <summary>
        /// 外部领用单明细ID
        /// </summary>
        public string? ExternalRequisitionDetailId { get; set; }

        /// <summary>
        /// 实际数量
        /// </summary>
        public int? ActualQuantity { get; set; }

        /// <summary>
        /// 实际零件名称
        /// </summary>
        public string? ActualPartName { get; set; }

        /// <summary>
        /// 实际规格
        /// </summary>
        public string? ActualSpecification { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
    }

    /// <summary>
    /// 零件使用统计DTO
    /// </summary>
    public class PartUsageStatisticsDto
    {
        /// <summary>
        /// 零件名称
        /// </summary>
        public string PartName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// 总成本
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 平均单价
        /// </summary>
        public decimal AverageUnitPrice { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedAt { get; set; }
    }
}
