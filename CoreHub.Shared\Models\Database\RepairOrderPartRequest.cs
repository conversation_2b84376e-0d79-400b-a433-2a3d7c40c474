using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace CoreHub.Shared.Models.Database
{
    /// <summary>
    /// 维修单零件更换申请实体
    /// </summary>
    [SugarTable("RepairOrderPartRequests")]
    public class RepairOrderPartRequest
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 关联的维修单ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "维修单不能为空")]
        public int RepairOrderId { get; set; }

        /// <summary>
        /// 零件名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false)]
        [Required(ErrorMessage = "零件名称不能为空")]
        [StringLength(100, ErrorMessage = "零件名称长度不能超过100个字符")]
        public string PartName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "规格型号长度不能超过200个字符")]
        public string? Specification { get; set; }

        /// <summary>
        /// 申请数量
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "申请数量不能为空")]
        [Range(1, 9999, ErrorMessage = "申请数量必须在1-9999之间")]
        public int RequestedQuantity { get; set; } = 1;

        /// <summary>
        /// 计量单位
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false)]
        [Required(ErrorMessage = "计量单位不能为空")]
        [StringLength(20, ErrorMessage = "计量单位长度不能超过20个字符")]
        public string Unit { get; set; } = "个";

        /// <summary>
        /// 更换原因
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true)]
        [StringLength(500, ErrorMessage = "更换原因长度不能超过500个字符")]
        public string? Reason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [SugarColumn(Length = 1000, IsNullable = true)]
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remark { get; set; }

        /// <summary>
        /// 状态（1=申请中,2=已批准,3=已领用,4=已安装,5=已取消）
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public int Status { get; set; } = 1;

        /// <summary>
        /// 申请人ID
        /// </summary>
        [SugarColumn(IsNullable = false)]
        [Required(ErrorMessage = "申请人不能为空")]
        public int RequestedBy { get; set; }

        /// <summary>
        /// 申请时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime RequestedAt { get; set; } = DateTime.Now;

        #region 外部系统集成预留字段

        /// <summary>
        /// 外部系统零件编号
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        [StringLength(50, ErrorMessage = "外部系统零件编号长度不能超过50个字符")]
        public string? ExternalPartNumber { get; set; }

        /// <summary>
        /// 外部系统领用单明细ID
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        [StringLength(50, ErrorMessage = "外部系统领用单明细ID长度不能超过50个字符")]
        public string? ExternalRequisitionDetailId { get; set; }

        /// <summary>
        /// 实际领用数量
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ActualQuantity { get; set; }

        /// <summary>
        /// 实际领用名称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true)]
        [StringLength(100, ErrorMessage = "实际领用名称长度不能超过100个字符")]
        public string? ActualPartName { get; set; }

        /// <summary>
        /// 实际领用规格
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true)]
        [StringLength(200, ErrorMessage = "实际领用规格长度不能超过200个字符")]
        public string? ActualSpecification { get; set; }

        #endregion

        #region 审批和处理信息

        /// <summary>
        /// 批准人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? ApprovedBy { get; set; }

        /// <summary>
        /// 批准时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? ApprovedAt { get; set; }

        /// <summary>
        /// 发放人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? IssuedBy { get; set; }

        /// <summary>
        /// 发放时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? IssuedAt { get; set; }

        /// <summary>
        /// 安装人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? InstalledBy { get; set; }

        /// <summary>
        /// 安装时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? InstalledAt { get; set; }

        #endregion

        #region 成本信息

        /// <summary>
        /// 单价
        /// </summary>
        [SugarColumn(DecimalDigits = 2, IsNullable = true)]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 总成本
        /// </summary>
        [SugarColumn(DecimalDigits = 2, IsNullable = true)]
        public decimal? TotalCost { get; set; }

        /// <summary>
        /// 仓库出库单号
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true)]
        [StringLength(50, ErrorMessage = "仓库出库单号长度不能超过50个字符")]
        public string? WarehouseOrderNumber { get; set; }

        #endregion

        #region 系统字段

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 更新人ID
        /// </summary>
        [SugarColumn(IsNullable = true)]
        public int? UpdatedBy { get; set; }

        #endregion

        #region 导航属性

        /// <summary>
        /// 关联的维修单
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public RepairOrder? RepairOrder { get; set; }

        /// <summary>
        /// 申请人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Requester { get; set; }

        /// <summary>
        /// 批准人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Approver { get; set; }

        /// <summary>
        /// 发放人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Issuer { get; set; }

        /// <summary>
        /// 安装人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Installer { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Creator { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public User? Updater { get; set; }

        #endregion

        #region 计算属性

        /// <summary>
        /// 状态名称
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string StatusName => Status switch
        {
            1 => "申请中",
            2 => "已批准",
            3 => "已领用",
            4 => "已安装",
            5 => "已取消",
            _ => "未知"
        };

        /// <summary>
        /// 显示名称（用于界面显示）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DisplayName
        {
            get
            {
                var name = PartName;
                if (!string.IsNullOrWhiteSpace(Specification))
                {
                    name += $" ({Specification})";
                }
                return name;
            }
        }

        /// <summary>
        /// 数量描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string QuantityDescription => $"{RequestedQuantity} {Unit}";

        /// <summary>
        /// 实际数量描述
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string ActualQuantityDescription => ActualQuantity.HasValue ? $"{ActualQuantity} {Unit}" : "-";

        /// <summary>
        /// 是否可以编辑
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool CanEdit => Status == 1; // 只有申请中状态可以编辑

        /// <summary>
        /// 是否可以删除
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool CanDelete => Status == 1; // 只有申请中状态可以删除

        /// <summary>
        /// 是否可以批准
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool CanApprove => Status == 1;

        /// <summary>
        /// 是否可以发放
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool CanIssue => Status == 2;

        /// <summary>
        /// 是否可以安装
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool CanInstall => Status == 3;

        /// <summary>
        /// 是否可以取消
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool CanCancel => Status == 1 || Status == 2;

        /// <summary>
        /// 处理耗时（小时）
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public double? ProcessingHours
        {
            get
            {
                if (InstalledAt.HasValue)
                {
                    return (InstalledAt.Value - RequestedAt).TotalHours;
                }
                return null;
            }
        }

        /// <summary>
        /// 是否有外部系统信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool HasExternalInfo => !string.IsNullOrWhiteSpace(ExternalPartNumber) || 
                                       !string.IsNullOrWhiteSpace(ExternalRequisitionDetailId);

        /// <summary>
        /// 是否有实际信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool HasActualInfo => ActualQuantity.HasValue || 
                                     !string.IsNullOrWhiteSpace(ActualPartName) || 
                                     !string.IsNullOrWhiteSpace(ActualSpecification);

        /// <summary>
        /// 是否有成本信息
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public bool HasCostInfo => UnitPrice.HasValue || TotalCost.HasValue;

        #endregion
    }
}
