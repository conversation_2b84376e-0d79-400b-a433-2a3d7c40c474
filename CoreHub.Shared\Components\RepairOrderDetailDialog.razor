@using CoreHub.Shared.Services
@using CoreHub.Shared.Models.Database

@inject IRepairWorkflowService RepairWorkflowService
@inject IUserManagementService UserManagementService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudGrid>
            <MudItem xs="12">
                <MudText Typo="Typo.h6" Class="mb-4">
                    <MudIcon Icon="Icons.Material.Filled.Assignment" Class="mr-2" />
                    报修单详情 - @RepairOrderDetail.OrderNumber
                </MudText>
            </MudItem>

            <!-- 基本信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">基本信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudField Label="报修单号" Variant="Variant.Text">
                                    @RepairOrderDetail.OrderNumber
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="报修状态" Variant="Variant.Text">
                                    <MudChip T="string" Color="@GetStatusColor(RepairOrderDetail.Status)" Size="Size.Small">
                                        @RepairOrderDetail.StatusName
                                    </MudChip>
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="紧急程度" Variant="Variant.Text">
                                    <MudChip T="string" Color="@GetUrgencyColor(RepairOrderDetail.UrgencyLevel)" Size="Size.Small">
                                        @RepairOrderDetail.UrgencyLevelName
                                    </MudChip>
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="报修人" Variant="Variant.Text">
                                    @RepairOrderDetail.ReporterName
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="维修部门" Variant="Variant.Text">
                                    @RepairOrderDetail.MaintenanceDepartmentName
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="指派给" Variant="Variant.Text">
                                    @if (!string.IsNullOrEmpty(RepairOrderDetail.AssignedToName))
                                    {
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" />
                                            <MudText>@RepairOrderDetail.AssignedToName</MudText>
                                            @if (maintenancePersonnel != null)
                                            {
                                                <MudChip T="string" Color="Color.Info" Size="Size.Small">
                                                    维修人员
                                                </MudChip>
                                            }
                                        </MudStack>
                                    }
                                    else
                                    {
                                        <MudText Color="Color.Secondary">未指派</MudText>
                                    }
                                </MudField>
                            </MudItem>
                            @if (maintenancePersonnel != null)
                            {
                                <MudItem xs="12">
                                    <MudField Label="维修人员部门" Variant="Variant.Text">
                                        <MudChip T="string" Color="Color.Secondary" Size="Size.Small">
                                            @maintenancePersonnel.Department?.Name
                                        </MudChip>
                                    </MudField>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 设备信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">设备信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudField Label="设备名称" Variant="Variant.Text">
                                    @RepairOrderDetail.EquipmentName
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="设备编码" Variant="Variant.Text">
                                    @RepairOrderDetail.EquipmentCode
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="所属部门" Variant="Variant.Text">
                                    @RepairOrderDetail.EquipmentDepartmentName
                                </MudField>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudField Label="所在位置" Variant="Variant.Text">
                                    @RepairOrderDetail.EquipmentLocationName
                                </MudField>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 故障信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">故障信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12">
                                <MudField Label="故障描述" Variant="Variant.Text">
                                    <MudText Style="white-space: pre-wrap;">@RepairOrderDetail.FaultDescription</MudText>
                                </MudField>
                            </MudItem>
                            @if (!string.IsNullOrEmpty(RepairOrderDetail.Remark))
                            {
                                <MudItem xs="12">
                                    <MudField Label="补充说明" Variant="Variant.Text">
                                        <MudText Style="white-space: pre-wrap;">@RepairOrderDetail.Remark</MudText>
                                    </MudField>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 维修信息 -->
            @if (RepairOrderDetail.Status >= 2)
            {
                <MudItem xs="12">
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">维修信息</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudGrid>
                                @if (!string.IsNullOrEmpty(RepairOrderDetail.RepairDescription))
                                {
                                    <MudItem xs="12">
                                        <MudField Label="维修描述" Variant="Variant.Text">
                                            <MudText Style="white-space: pre-wrap;">@RepairOrderDetail.RepairDescription</MudText>
                                        </MudField>
                                    </MudItem>
                                }
                                @if (RepairOrderDetail.RepairCost.HasValue)
                                {
                                    <MudItem xs="12" md="6">
                                        <MudField Label="维修费用" Variant="Variant.Text">
                                            ¥@RepairOrderDetail.RepairCost.Value.ToString("F2")
                                        </MudField>
                                    </MudItem>
                                }
                                @if (!string.IsNullOrEmpty(RepairOrderDetail.PartsUsed))
                                {
                                    <MudItem xs="12" md="6">
                                        <MudField Label="使用配件" Variant="Variant.Text">
                                            @RepairOrderDetail.PartsUsed
                                        </MudField>
                                    </MudItem>
                                }
                                @if (!string.IsNullOrEmpty(RepairOrderDetail.TestResult))
                                {
                                    <MudItem xs="12">
                                        <MudField Label="测试结果" Variant="Variant.Text">
                                            <MudText Style="white-space: pre-wrap;">@RepairOrderDetail.TestResult</MudText>
                                        </MudField>
                                    </MudItem>
                                }
                            </MudGrid>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
            }

            <!-- 时间信息 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">时间信息</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudField Label="创建时间" Variant="Variant.Text">
                                    @RepairOrderDetail.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                                </MudField>
                            </MudItem>
                            @if (RepairOrderDetail.AssignedAt.HasValue)
                            {
                                <MudItem xs="12" md="6">
                                    <MudField Label="指派时间" Variant="Variant.Text">
                                        @RepairOrderDetail.AssignedAt.Value.ToString("yyyy-MM-dd HH:mm")
                                    </MudField>
                                </MudItem>
                            }
                            @if (RepairOrderDetail.StartedAt.HasValue)
                            {
                                <MudItem xs="12" md="6">
                                    <MudField Label="开始维修时间" Variant="Variant.Text">
                                        @RepairOrderDetail.StartedAt.Value.ToString("yyyy-MM-dd HH:mm")
                                    </MudField>
                                </MudItem>
                            }
                            @if (RepairOrderDetail.CompletedAt.HasValue)
                            {
                                <MudItem xs="12" md="6">
                                    <MudField Label="完成时间" Variant="Variant.Text">
                                        @RepairOrderDetail.CompletedAt.Value.ToString("yyyy-MM-dd HH:mm")
                                    </MudField>
                                </MudItem>
                            }
                            @if (RepairOrderDetail.CancelledAt.HasValue)
                            {
                                <MudItem xs="12" md="6">
                                    <MudField Label="作废时间" Variant="Variant.Text">
                                        @RepairOrderDetail.CancelledAt.Value.ToString("yyyy-MM-dd HH:mm")
                                    </MudField>
                                </MudItem>
                                <MudItem xs="12" md="6">
                                    <MudField Label="作废原因" Variant="Variant.Text">
                                        @RepairOrderDetail.CancelReason
                                    </MudField>
                                </MudItem>
                            }
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- 评价信息 -->
            @if (RepairOrderDetail.Status == 3 && RepairOrderDetail.ReporterRating.HasValue)
            {
                <MudItem xs="12">
                    <MudCard>
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">评价信息</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudGrid>
                                <MudItem xs="12" md="6">
                                    <MudField Label="评价等级" Variant="Variant.Text">
                                        <MudRating ReadOnly="true" SelectedValue="@RepairOrderDetail.ReporterRating.Value" />
                                    </MudField>
                                </MudItem>
                                @if (!string.IsNullOrEmpty(RepairOrderDetail.ReporterComment))
                                {
                                    <MudItem xs="12">
                                        <MudField Label="评价意见" Variant="Variant.Text">
                                            <MudText Style="white-space: pre-wrap;">@RepairOrderDetail.ReporterComment</MudText>
                                        </MudField>
                                    </MudItem>
                                }
                            </MudGrid>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
            }

            <!-- 操作历史 -->
            <MudItem xs="12">
                <MudCard>
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">操作历史</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (loadingHistory)
                        {
                            <MudProgressLinear Indeterminate="true" />
                            <MudText Typo="Typo.body2" Class="mt-2">正在加载历史记录...</MudText>
                        }
                        else if (workflowHistory.Any())
                        {
                            <MudTimeline TimelineOrientation="TimelineOrientation.Vertical">
                                @foreach (var history in workflowHistory.Take(10))
                                {
                                    <MudTimelineItem>
                                        <ItemOpposite>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                @history.FormattedTime
                                            </MudText>
                                        </ItemOpposite>
                                        <ItemDot>
                                            <MudIcon Icon="@history.ActionIcon" Color="@GetHistoryColor(history)" Size="Size.Small" />
                                        </ItemDot>
                                        <ItemContent>
                                            <MudPaper Class="pa-2" Elevation="1">
                                                <div class="d-flex justify-space-between align-center mb-1">
                                                    <MudText Typo="Typo.subtitle2">@history.Action</MudText>
                                                    @if (history.IsStatusChange)
                                                    {
                                                        <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                                            @history.StatusChangeDescription
                                                        </MudChip>
                                                    }
                                                </div>
                                                @if (!string.IsNullOrEmpty(history.Comment))
                                                {
                                                    <MudText Typo="Typo.body2" Class="mb-1">@history.Comment</MudText>
                                                }
                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                    @history.UserName | @history.DetailedTime
                                                </MudText>
                                            </MudPaper>
                                        </ItemContent>
                                    </MudTimelineItem>
                                }
                            </MudTimeline>

                            @if (workflowHistory.Count > 10)
                            {
                                <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mt-2">
                                    显示最近 10 条记录，共 @workflowHistory.Count 条
                                </MudText>
                            }
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info">
                                暂无操作历史记录
                            </MudAlert>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Close">关闭</MudButton>
        @if (RepairOrderDetail.Status == 3 && !RepairOrderDetail.ReporterRating.HasValue)
        {
            <MudButton Color="Color.Primary" 
                     Variant="Variant.Filled" 
                     StartIcon="Icons.Material.Filled.Star"
                     OnClick="RateRepair">
                评价维修
            </MudButton>
        }
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public RepairOrderDetailDto RepairOrderDetail { get; set; } = new();

    private User? maintenancePersonnel = null;
    private bool loadingHistory = true;
    private List<WorkflowHistoryDto> workflowHistory = new();

    protected override async Task OnInitializedAsync()
    {
        var loadPersonnelTask = LoadMaintenancePersonnelInfo();
        var loadHistoryTask = LoadWorkflowHistory();

        await Task.WhenAll(loadPersonnelTask, loadHistoryTask);
    }

    private async Task LoadMaintenancePersonnelInfo()
    {
        if (RepairOrderDetail.AssignedTo.HasValue)
        {
            try
            {
                maintenancePersonnel = await UserManagementService.GetUserByIdAsync(RepairOrderDetail.AssignedTo.Value);
            }
            catch (Exception)
            {
                // 如果获取维修人员信息失败，不影响对话框显示
                maintenancePersonnel = null;
            }
        }
    }

    private async Task LoadWorkflowHistory()
    {
        loadingHistory = true;
        try
        {
            workflowHistory = await RepairWorkflowService.GetWorkflowHistoryAsync(RepairOrderDetail.Id);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载历史记录失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loadingHistory = false;
        }
    }

    private Color GetStatusColor(int status)
    {
        return CoreHub.Shared.Utils.RepairOrderStatusHelper.GetStatusColor(status);
    }

    private Color GetUrgencyColor(int urgencyLevel)
    {
        return urgencyLevel switch
        {
            1 => Color.Error,    // 紧急
            2 => Color.Warning,  // 高
            3 => Color.Info,     // 中
            4 => Color.Default,  // 低
            _ => Color.Default
        };
    }

    private Color GetHistoryColor(WorkflowHistoryDto history)
    {
        return history.Action.ToLower() switch
        {
            var a when a.Contains("创建") => Color.Primary,
            var a when a.Contains("分配") => Color.Info,
            var a when a.Contains("开始") || a.Contains("接受") => Color.Success,
            var a when a.Contains("暂停") => Color.Warning,
            var a when a.Contains("恢复") => Color.Success,
            var a when a.Contains("完成") => Color.Success,
            var a when a.Contains("关闭") => Color.Secondary,
            var a when a.Contains("作废") || a.Contains("取消") => Color.Error,
            var a when a.Contains("审批") => Color.Primary,
            var a when a.Contains("支援") || a.Contains("帮助") => Color.Warning,
            _ => Color.Default
        };
    }

    private void Close()
    {
        MudDialog.Close();
    }

    private void RateRepair()
    {
        // TODO: 实现评价功能
        MudDialog.Close(DialogResult.Ok("rate_repair"));
    }
}
