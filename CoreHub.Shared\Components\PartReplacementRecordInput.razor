@using CoreHub.Shared.Models.Dto
@using FluentValidation

<MudCard Class="mb-4">
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
                零件更换记录
                @if (PartRecords.HasAnyParts)
                {
                    <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                        @PartRecords.TotalCount 项
                    </MudChip>
                }
            </MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            <MudButton Variant="Variant.Outlined" 
                       Color="Color.Primary" 
                       StartIcon="@Icons.Material.Filled.Add"
                       OnClick="ShowAddPartDialog"
                       Size="Size.Small">
                添加零件
            </MudButton>
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent>
        @if (!PartRecords.HasAnyParts)
        {
            <MudAlert Severity="Severity.Info" Class="mb-4">
                <MudText>暂无零件更换记录。如需更换零件，请点击"添加零件"按钮。</MudText>
            </MudAlert>
        }
        else
        {
            <MudTable Items="@PartRecords.PartRequests" 
                      Dense="true" 
                      Hover="true" 
                      Breakpoint="Breakpoint.Sm"
                      Class="mb-4">
                <HeaderContent>
                    <MudTh>零件名称</MudTh>
                    <MudTh>规格型号</MudTh>
                    <MudTh>数量</MudTh>
                    <MudTh>状态</MudTh>
                    <MudTh>更换原因</MudTh>
                    <MudTh Style="width: 120px;">操作</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="零件名称">
                        <MudText Typo="Typo.body2">@context.PartName</MudText>
                    </MudTd>
                    <MudTd DataLabel="规格型号">
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            @(string.IsNullOrWhiteSpace(context.Specification) ? "-" : context.Specification)
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="数量">
                        <MudText Typo="Typo.body2">@context.QuantityDescription</MudText>
                    </MudTd>
                    <MudTd DataLabel="状态">
                        <MudChip T="string" 
                                 Color="@GetStatusColor(context.Status)" 
                                 Size="Size.Small">
                            @context.StatusName
                        </MudChip>
                    </MudTd>
                    <MudTd DataLabel="更换原因">
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            @(string.IsNullOrWhiteSpace(context.Reason) ? "-" : 
                              (context.Reason.Length > 20 ? context.Reason.Substring(0, 20) + "..." : context.Reason))
                        </MudText>
                    </MudTd>
                    <MudTd DataLabel="操作">
                        <MudStack Row Spacing="1">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                           Size="Size.Small" 
                                           Color="Color.Primary"
                                           OnClick="() => ShowEditPartDialog(context)"
                                           Disabled="@(!context.CanEdit)"
                                           Title="编辑" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                           Size="Size.Small" 
                                           Color="Color.Error"
                                           OnClick="() => DeletePart(context.TempId)"
                                           Disabled="@(!context.CanDelete)"
                                           Title="删除" />
                        </MudStack>
                    </MudTd>
                </RowTemplate>
            </MudTable>

            <!-- 统计信息 -->
            <MudGrid>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Primary">@PartRecords.TotalCount</MudText>
                        <MudText Typo="Typo.caption">总记录数</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Warning">@PartRecords.PendingCount</MudText>
                        <MudText Typo="Typo.caption">申请中</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Success">@PartRecords.CompletedCount</MudText>
                        <MudText Typo="Typo.caption">已完成</MudText>
                    </MudPaper>
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudPaper Class="pa-4 text-center">
                        <MudText Typo="Typo.h6" Color="Color.Info">@PartRecords.TotalRequestedQuantity</MudText>
                        <MudText Typo="Typo.caption">总数量</MudText>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        }
    </MudCardContent>
</MudCard>

<!-- 添加/编辑零件对话框 -->
<MudDialog @bind-IsVisible="showPartDialog" Options="dialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-2" />
            @(isEditMode ? "编辑零件记录" : "添加零件记录")
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudForm @ref="partForm" Model="@currentPart" Validation="@(new PartReplacementRequestValidator())">
            <MudGrid>
                <MudItem xs="12" md="8">
                    <MudTextField @bind-Value="currentPart.PartName"
                                  For="@(() => currentPart.PartName)"
                                  Label="零件名称"
                                  Required="true"
                                  Immediate="true"
                                  HelperText="请输入零件的具体名称" />
                </MudItem>
                <MudItem xs="12" md="4">
                    <MudTextField @bind-Value="currentPart.Unit"
                                  For="@(() => currentPart.Unit)"
                                  Label="计量单位"
                                  Required="true"
                                  Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentPart.Specification"
                                  For="@(() => currentPart.Specification)"
                                  Label="规格型号"
                                  Immediate="true"
                                  HelperText="请输入零件的规格、型号等详细信息" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudNumericField @bind-Value="currentPart.RequestedQuantity"
                                     For="@(() => currentPart.RequestedQuantity)"
                                     Label="申请数量"
                                     Required="true"
                                     Min="1"
                                     Max="9999"
                                     Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentPart.Reason"
                                  For="@(() => currentPart.Reason)"
                                  Label="更换原因"
                                  Lines="2"
                                  Immediate="true"
                                  HelperText="请说明为什么需要更换此零件" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="currentPart.Remark"
                                  For="@(() => currentPart.Remark)"
                                  Label="备注"
                                  Lines="2"
                                  Immediate="true"
                                  HelperText="其他需要说明的信息" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CancelPartDialog">取消</MudButton>
        <MudButton Color="Color.Primary" 
                   Variant="Variant.Filled" 
                   OnClick="SavePart"
                   StartIcon="@Icons.Material.Filled.Save">
            @(isEditMode ? "保存" : "添加")
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [Parameter] public PartReplacementRequestCollectionDto PartRecords { get; set; } = new();
    [Parameter] public EventCallback<PartReplacementRequestCollectionDto> PartRecordsChanged { get; set; }
    [Parameter] public bool ReadOnly { get; set; } = false;
    [Parameter] public int RepairOrderId { get; set; }
    [Parameter] public int CurrentUserId { get; set; }

    private MudForm partForm = null!;
    private bool showPartDialog = false;
    private bool isEditMode = false;
    private PartReplacementRequestDto currentPart = new();
    private string editingTempId = string.Empty;

    private DialogOptions dialogOptions = new()
    {
        MaxWidth = MaxWidth.Medium,
        FullWidth = true,
        CloseButton = true,
        BackdropClick = false
    };

    private Color GetStatusColor(int status) => CoreHub.Shared.Utils.PartRequestStatusHelper.GetStatusColor(status);

    private void ShowAddPartDialog()
    {
        if (ReadOnly) return;

        isEditMode = false;
        currentPart = new PartReplacementRequestDto
        {
            RepairOrderId = RepairOrderId,
            RequestedBy = CurrentUserId
        };
        showPartDialog = true;
    }

    private void ShowEditPartDialog(PartReplacementRequestDto part)
    {
        if (ReadOnly || !part.CanEdit) return;

        isEditMode = true;
        editingTempId = part.TempId;
        currentPart = part.Clone();
        showPartDialog = true;
    }

    private void CancelPartDialog()
    {
        showPartDialog = false;
        currentPart = new PartReplacementRequestDto
        {
            RepairOrderId = RepairOrderId,
            RequestedBy = CurrentUserId
        };
        editingTempId = string.Empty;
    }

    private async Task SavePart()
    {
        await partForm.Validate();
        if (!partForm.IsValid) return;

        if (isEditMode)
        {
            // 更新现有记录
            currentPart.TempId = editingTempId;
            PartRecords.UpdatePart(currentPart);
        }
        else
        {
            // 添加新记录
            PartRecords.AddPart(currentPart);
        }

        await PartRecordsChanged.InvokeAsync(PartRecords);
        CancelPartDialog();
    }

    private async Task DeletePart(string tempId)
    {
        if (ReadOnly) return;

        var part = PartRecords.GetPart(tempId);
        if (part != null && part.CanDelete)
        {
            PartRecords.RemovePart(tempId);
            await PartRecordsChanged.InvokeAsync(PartRecords);
        }
    }

    public class PartReplacementRequestValidator : AbstractValidator<PartReplacementRequestDto>
    {
        public PartReplacementRequestValidator()
        {
            RuleFor(x => x.PartName)
                .NotEmpty().WithMessage("零件名称不能为空")
                .MaximumLength(100).WithMessage("零件名称长度不能超过100个字符");

            RuleFor(x => x.Specification)
                .MaximumLength(200).WithMessage("规格型号长度不能超过200个字符");

            RuleFor(x => x.RequestedQuantity)
                .GreaterThan(0).WithMessage("申请数量必须大于0")
                .LessThanOrEqualTo(9999).WithMessage("申请数量不能超过9999");

            RuleFor(x => x.Unit)
                .NotEmpty().WithMessage("计量单位不能为空")
                .MaximumLength(20).WithMessage("计量单位长度不能超过20个字符");

            RuleFor(x => x.Reason)
                .MaximumLength(500).WithMessage("更换原因长度不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<PartReplacementRequestDto>.CreateWithOptions((PartReplacementRequestDto)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
