@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using FluentValidation
@inject IEquipmentModelService EquipmentModelService
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudForm @ref="form" Model="@EquipmentModel" Validation="@(new EquipmentModelValidator())">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="EquipmentModel.Code"
                                For="@(() => EquipmentModel.Code)"
                                Label="型号编码"
                                Required="true"
                                Immediate="true"
                                Disabled="@IsEdit" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="EquipmentModel.Name"
                                For="@(() => EquipmentModel.Name)"
                                Label="型号名称"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="EquipmentModel.Category"
                                For="@(() => EquipmentModel.Category)"
                                Label="设备类别"
                                Required="true"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="EquipmentModel.Brand"
                                For="@(() => EquipmentModel.Brand)"
                                Label="品牌"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="EquipmentModel.Model"
                                For="@(() => EquipmentModel.Model)"
                                Label="规格型号"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudSwitch T="bool" @bind-Checked="EquipmentModel.IsEnabled"
                             Label="启用状态"
                             Color="Color.Primary" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="EquipmentModel.Specifications"
                                For="@(() => EquipmentModel.Specifications)"
                                Label="技术规格"
                                Lines="3"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="EquipmentModel.Description"
                                For="@(() => EquipmentModel.Description)"
                                Label="描述"
                                Lines="2"
                                Immediate="true" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="EquipmentModel.Remark"
                                For="@(() => EquipmentModel.Remark)"
                                Label="备注"
                                Lines="2"
                                Immediate="true" />
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">取消</MudButton>
        <MudButton Color="Color.Primary" 
                 Variant="Variant.Filled" 
                 OnClick="Submit"
                 Disabled="@saving">
            @if (saving)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">保存中...</MudText>
            }
            else
            {
                <MudText>@(IsEdit ? "更新" : "创建")</MudText>
            }
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; } = null!;
    [Parameter] public EquipmentModel EquipmentModel { get; set; } = new();
    [Parameter] public bool IsEdit { get; set; } = false;

    private MudForm form = null!;
    private bool saving = false;

    protected override Task OnInitializedAsync()
    {
        if (!IsEdit)
        {
            EquipmentModel.IsEnabled = true;
        }
        return Task.CompletedTask;
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task Submit()
    {
        await form.Validate();
        if (!form.IsValid) return;

        saving = true;
        try
        {
            (bool IsSuccess, string ErrorMessage) result;
            
            if (IsEdit)
            {
                EquipmentModel.UpdatedAt = DateTime.Now;
                result = await EquipmentModelService.UpdateEquipmentModelAsync(EquipmentModel);
            }
            else
            {
                result = await EquipmentModelService.CreateEquipmentModelAsync(EquipmentModel);
            }

            if (result.IsSuccess)
            {
                Snackbar.Add($"设备型号{(IsEdit ? "更新" : "创建")}成功", Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                Snackbar.Add($"操作失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    public class EquipmentModelValidator : AbstractValidator<EquipmentModel>
    {
        public EquipmentModelValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("型号编码不能为空")
                .MaximumLength(50).WithMessage("型号编码长度不能超过50个字符");

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("型号名称不能为空")
                .MaximumLength(100).WithMessage("型号名称长度不能超过100个字符");

            RuleFor(x => x.Category)
                .NotEmpty().WithMessage("设备类别不能为空")
                .MaximumLength(50).WithMessage("设备类别长度不能超过50个字符");

            RuleFor(x => x.Brand)
                .MaximumLength(100).WithMessage("品牌长度不能超过100个字符");

            RuleFor(x => x.Model)
                .MaximumLength(100).WithMessage("规格型号长度不能超过100个字符");

            RuleFor(x => x.Specifications)
                .MaximumLength(1000).WithMessage("技术规格长度不能超过1000个字符");

            RuleFor(x => x.Description)
                .MaximumLength(500).WithMessage("描述长度不能超过500个字符");

            RuleFor(x => x.Remark)
                .MaximumLength(1000).WithMessage("备注长度不能超过1000个字符");
        }

        public Func<object, string, Task<IEnumerable<string>>> ValidateValue => async (model, propertyName) =>
        {
            var result = await ValidateAsync(ValidationContext<EquipmentModel>.CreateWithOptions((EquipmentModel)model, x => x.IncludeProperties(propertyName)));
            if (result.IsValid)
                return Array.Empty<string>();
            return result.Errors.Select(e => e.ErrorMessage);
        };
    }
}
