using CoreHub.Shared.Models.Database;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 报修工作流服务接口
    /// </summary>
    public interface IRepairWorkflowService
    {
        #region 状态转换

        /// <summary>
        /// 获取可用的状态转换选项
        /// </summary>
        Task<List<StatusTransitionOption>> GetAvailableStatusTransitionsAsync(int repairOrderId, int userId);

        /// <summary>
        /// 执行状态转换
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ExecuteStatusTransitionAsync(int repairOrderId, int newStatus, int userId, string? comment = null);

        /// <summary>
        /// 检查状态转换是否有效
        /// </summary>
        Task<bool> IsValidStatusTransitionAsync(int currentStatus, int newStatus, int userId, int repairOrderId);

        #endregion

        #region 工作流操作

        /// <summary>
        /// 接受报修单（技术员）
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> AcceptRepairOrderAsync(int repairOrderId, int technicianId);

        /// <summary>
        /// 开始维修工作
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> StartRepairWorkAsync(int repairOrderId, int technicianId, string? startComment = null);

        /// <summary>
        /// 暂停维修工作
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> PauseRepairWorkAsync(int repairOrderId, int technicianId, string pauseReason);

        /// <summary>
        /// 恢复维修工作
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ResumeRepairWorkAsync(int repairOrderId, int technicianId);

        /// <summary>
        /// 完成维修工作
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CompleteRepairWorkAsync(int repairOrderId, int technicianId, CompleteRepairDto completeData);

        /// <summary>
        /// 请求支援
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> RequestSupportAsync(int repairOrderId, int technicianId, string supportReason);

        /// <summary>
        /// 升级报修单（提高优先级）
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> EscalateRepairOrderAsync(int repairOrderId, int userId, string escalationReason);

        #endregion

        #region 审批流程

        /// <summary>
        /// 提交完成审批
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> SubmitCompletionApprovalAsync(int repairOrderId, int technicianId);

        /// <summary>
        /// 审批完成
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> ApproveCompletionAsync(int repairOrderId, int managerId, bool approved, string? comment = null);

        /// <summary>
        /// 关闭报修单
        /// </summary>
        Task<(bool IsSuccess, string ErrorMessage)> CloseRepairOrderAsync(int repairOrderId, int userId, string? closeComment = null);

        #endregion

        #region 工作流历史

        /// <summary>
        /// 获取报修单工作流历史
        /// </summary>
        Task<List<WorkflowHistoryDto>> GetWorkflowHistoryAsync(int repairOrderId);

        /// <summary>
        /// 添加工作流历史记录
        /// </summary>
        Task<bool> AddWorkflowHistoryAsync(int repairOrderId, int userId, string action, string? comment = null);

        /// <summary>
        /// 添加工作流历史记录（包含状态变更信息）
        /// </summary>
        Task<bool> AddWorkflowHistoryAsync(int repairOrderId, int userId, string action, string? comment, int? fromStatus, int? toStatus, string? additionalData = null);

        #endregion
    }

    /// <summary>
    /// 状态转换选项
    /// </summary>
    public class StatusTransitionOption
    {
        public int FromStatus { get; set; }
        public int ToStatus { get; set; }
        public string ToStatusName { get; set; } = string.Empty;
        public string ActionName { get; set; } = string.Empty;
        public string ActionDescription { get; set; } = string.Empty;
        public bool RequiresComment { get; set; }
        public bool RequiresApproval { get; set; }
        public string RequiredRole { get; set; } = string.Empty;
    }

    /// <summary>
    /// 完成维修数据DTO
    /// </summary>
    public class CompleteRepairDto
    {
        public string RepairDescription { get; set; } = string.Empty;
        public decimal? RepairCost { get; set; }
        public string? PartsUsed { get; set; }
        public string? TestResult { get; set; }
        public bool RequiresApproval { get; set; }
        public string? CompletionComment { get; set; }
    }

    /// <summary>
    /// 工作流历史DTO（保持向后兼容）
    /// </summary>
    public class WorkflowHistoryDto : RepairWorkflowHistoryDto
    {
        // 继承RepairWorkflowHistoryDto以保持向后兼容
    }

    /// <summary>
    /// 报修单状态枚举 - 已废弃，请使用 RepairOrderStatusHelper
    /// </summary>
    [Obsolete("请使用 CoreHub.Shared.Utils.RepairOrderStatusHelper 替代")]
    public static class RepairOrderStatus
    {
        public const int Pending = 1;      // 待处理
        public const int InProgress = 2;   // 处理中
        public const int Completed = 3;    // 已完成
        public const int Cancelled = 4;    // 已作废
        public const int Closed = 5;       // 已关闭
        public const int Paused = 6;       // 已暂停
        public const int PendingApproval = 7; // 待审批

        public static string GetStatusName(int status)
        {
            return CoreHub.Shared.Utils.RepairOrderStatusHelper.GetStatusName(status);
        }
    }

    /// <summary>
    /// 工种分类常量
    /// </summary>
    public static class JobCategories
    {
        public const string Production = "生产";
        public const string Maintenance = "维修";
        public const string Management = "管理";
        public const string Support = "支持";
    }
}
