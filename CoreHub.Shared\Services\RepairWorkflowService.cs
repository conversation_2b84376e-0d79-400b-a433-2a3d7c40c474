using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 报修工作流服务实现
    /// </summary>
    public class RepairWorkflowService : IRepairWorkflowService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<RepairWorkflowService> _logger;
        private readonly IRepairOrderService _repairOrderService;
        private readonly IJobTypeService _jobTypeService;

        public RepairWorkflowService(
            DatabaseContext dbContext,
            ILogger<RepairWorkflowService> logger,
            IRepairOrderService repairOrderService,
            IJobTypeService jobTypeService)
        {
            _dbContext = dbContext;
            _logger = logger;
            _repairOrderService = repairOrderService;
            _jobTypeService = jobTypeService;
        }

        public async Task<List<StatusTransitionOption>> GetAvailableStatusTransitionsAsync(int repairOrderId, int userId)
        {
            try
            {
                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null) return new List<StatusTransitionOption>();

                var transitions = new List<StatusTransitionOption>();
                var currentStatus = repairOrder.Status;

                // 检查用户权限
                var canManage = await _repairOrderService.CanUserManageRepairOrderAsync(userId, repairOrderId);
                var canProcess = await _repairOrderService.CanUserProcessRepairOrderAsync(userId, repairOrderId);
                var isAssigned = repairOrder.AssignedTo == userId;

                // 获取用户工种
                var userJobTypes = await _jobTypeService.GetUserJobTypesWithDetailsAsync(userId);
                var hasManagerJobType = userJobTypes.Any(ujt => 
                    ujt.JobType.Category == JobCategories.Management && ujt.UserJobType.IsEnabled);
                var hasMaintenanceJobType = userJobTypes.Any(ujt => 
                    ujt.JobType.Category == JobCategories.Maintenance && ujt.UserJobType.IsEnabled);

                // 定义状态转换规则
                switch (currentStatus)
                {
                    case RepairOrderStatus.Pending:
                        if (canManage)
                        {
                            transitions.Add(new StatusTransitionOption
                            {
                                FromStatus = currentStatus,
                                ToStatus = RepairOrderStatus.InProgress,
                                ToStatusName = "处理中",
                                ActionName = "开始处理",
                                ActionDescription = "分配技术员并开始处理",
                                RequiresComment = false,
                                RequiredRole = "管理员"
                            });
                        }
                        if (isAssigned && hasMaintenanceJobType)
                        {
                            transitions.Add(new StatusTransitionOption
                            {
                                FromStatus = currentStatus,
                                ToStatus = RepairOrderStatus.InProgress,
                                ToStatusName = "处理中",
                                ActionName = "接受任务",
                                ActionDescription = "接受分配的维修任务",
                                RequiresComment = false,
                                RequiredRole = "技术员"
                            });
                        }
                        if (canManage)
                        {
                            transitions.Add(new StatusTransitionOption
                            {
                                FromStatus = currentStatus,
                                ToStatus = RepairOrderStatus.Cancelled,
                                ToStatusName = "已作废",
                                ActionName = "作废",
                                ActionDescription = "作废此报修单",
                                RequiresComment = true,
                                RequiredRole = "管理员"
                            });
                        }
                        break;

                    case RepairOrderStatus.InProgress:
                        if (isAssigned && hasMaintenanceJobType)
                        {
                            transitions.Add(new StatusTransitionOption
                            {
                                FromStatus = currentStatus,
                                ToStatus = RepairOrderStatus.PendingConfirmation, // 6
                                ToStatusName = "待确认",
                                ActionName = "完成维修",
                                ActionDescription = "标记维修工作已完成，等待报修人确认",
                                RequiresComment = false,
                                RequiredRole = "技术员"
                            });


                        }
                        if (canManage)
                        {
                            transitions.Add(new StatusTransitionOption
                            {
                                FromStatus = currentStatus,
                                ToStatus = RepairOrderStatus.Cancelled,
                                ToStatusName = "已作废",
                                ActionName = "作废",
                                ActionDescription = "作废此报修单",
                                RequiresComment = true,
                                RequiredRole = "管理员"
                            });
                        }
                        break;



                    case RepairOrderStatus.PendingConfirmation: // 6 - 待确认状态
                        // 检查是否为维修完成后的确认状态还是重修要求的确认状态
                        // 通过检查最近的工作流历史来判断
                        var recentHistory = await GetWorkflowHistoryAsync(repairOrderId);
                        var lastEntry = recentHistory.FirstOrDefault();
                        bool isReworkRequest = lastEntry?.Action?.Contains("要求重新维修") == true;

                        if (isReworkRequest)
                        {
                            // 重修要求状态 - 维修人员可以确认是否接受重修
                            if (repairOrder.AssignedTo == userId || canManage)
                            {
                                transitions.Add(new StatusTransitionOption
                                {
                                    FromStatus = currentStatus,
                                    ToStatus = RepairOrderStatus.InProgress,
                                    ToStatusName = "处理中",
                                    ActionName = "接受重修",
                                    ActionDescription = "接受重修要求，重新开始维修",
                                    RequiresComment = false,
                                    RequiredRole = "维修人员"
                                });

                                transitions.Add(new StatusTransitionOption
                                {
                                    FromStatus = currentStatus,
                                    ToStatus = RepairOrderStatus.Completed,
                                    ToStatusName = "已完成",
                                    ActionName = "拒绝重修",
                                    ActionDescription = "拒绝重修要求，维持完成状态",
                                    RequiresComment = true,
                                    RequiredRole = "维修人员"
                                });
                            }
                        }
                        else
                        {
                            // 维修完成确认状态 - 报修人可以确认维修结果
                            if (repairOrder.ReporterId == userId)
                            {
                                transitions.Add(new StatusTransitionOption
                                {
                                    FromStatus = currentStatus,
                                    ToStatus = RepairOrderStatus.Completed,
                                    ToStatusName = "已完成",
                                    ActionName = "确认完成",
                                    ActionDescription = "确认维修工作已完成",
                                    RequiresComment = false,
                                    RequiredRole = "报修人"
                                });

                                transitions.Add(new StatusTransitionOption
                                {
                                    FromStatus = currentStatus,
                                    ToStatus = RepairOrderStatus.InProgress,
                                    ToStatusName = "处理中",
                                    ActionName = "要求重新维修",
                                    ActionDescription = "维修不满意，要求重新维修",
                                    RequiresComment = true,
                                    RequiredRole = "报修人"
                                });
                            }
                        }
                        break;

                    case RepairOrderStatus.Completed:
                        if (canManage)
                        {
                            transitions.Add(new StatusTransitionOption
                            {
                                FromStatus = currentStatus,
                                ToStatus = RepairOrderStatus.Closed,
                                ToStatusName = "已关闭",
                                ActionName = "关闭",
                                ActionDescription = "关闭此报修单",
                                RequiresComment = false,
                                RequiredRole = "管理员"
                            });

                            transitions.Add(new StatusTransitionOption
                            {
                                FromStatus = currentStatus,
                                ToStatus = RepairOrderStatus.InProgress,
                                ToStatusName = "处理中",
                                ActionName = "重新处理",
                                ActionDescription = "重新开始处理",
                                RequiresComment = true,
                                RequiredRole = "管理员"
                            });
                        }
                        break;
                }

                return transitions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取状态转换选项失败: {repairOrderId}, {userId}", repairOrderId, userId);
                return new List<StatusTransitionOption>();
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ExecuteStatusTransitionAsync(int repairOrderId, int newStatus, int userId, string? comment = null)
        {
            try
            {
                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                // 验证状态转换是否有效
                if (!await IsValidStatusTransitionAsync(repairOrder.Status, newStatus, userId, repairOrderId))
                {
                    return (false, "无效的状态转换");
                }

                // 执行状态更新
                var result = await _repairOrderService.UpdateRepairOrderStatusAsync(repairOrderId, newStatus);

                if (result.IsSuccess)
                {
                    // 记录工作流历史（包含状态变更信息）
                    await AddWorkflowHistoryAsync(repairOrderId, userId,
                        $"状态从 {RepairOrderStatus.GetStatusName(repairOrder.Status)} 变更为 {RepairOrderStatus.GetStatusName(newStatus)}",
                        comment, repairOrder.Status, newStatus);

                    _logger.LogInformation("状态转换成功: 报修单 {repairOrderId} 从 {oldStatus} 变更为 {newStatus}",
                        repairOrderId, repairOrder.Status, newStatus);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行状态转换失败: {repairOrderId}", repairOrderId);
                return (false, $"状态转换失败: {ex.Message}");
            }
        }

        public async Task<bool> IsValidStatusTransitionAsync(int currentStatus, int newStatus, int userId, int repairOrderId)
        {
            try
            {
                var availableTransitions = await GetAvailableStatusTransitionsAsync(repairOrderId, userId);
                return availableTransitions.Any(t => t.FromStatus == currentStatus && t.ToStatus == newStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证状态转换失败: {currentStatus} -> {newStatus}", currentStatus, newStatus);
                return false;
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> AcceptRepairOrderAsync(int repairOrderId, int technicianId)
        {
            try
            {
                var result = await _repairOrderService.AssignRepairOrderAsync(repairOrderId, technicianId);
                if (result.IsSuccess)
                {
                    await AddWorkflowHistoryAsync(repairOrderId, technicianId, "接受维修任务");
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "接受报修单失败: {repairOrderId}", repairOrderId);
                return (false, $"接受失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> StartRepairWorkAsync(int repairOrderId, int technicianId, string? startComment = null)
        {
            try
            {
                // 获取当前报修单状态
                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                var result = await _repairOrderService.StartRepairAsync(repairOrderId);
                if (result.IsSuccess)
                {
                    await AddWorkflowHistoryAsync(repairOrderId, technicianId, "开始维修工作", startComment, repairOrder.Status, 2);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "开始维修工作失败: {repairOrderId}", repairOrderId);
                return (false, $"开始失败: {ex.Message}");
            }
        }





        public async Task<(bool IsSuccess, string ErrorMessage)> CompleteRepairWorkAsync(int repairOrderId, int technicianId, CompleteRepairDto completeData)
        {
            try
            {
                // 获取当前报修单状态
                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                var result = await _repairOrderService.CompleteRepairAsync(
                    repairOrderId,
                    completeData.RepairDescription,
                    completeData.RepairCost,
                    completeData.PartsUsed,
                    completeData.TestResult);

                if (result.IsSuccess)
                {
                    // 序列化完成数据作为附加信息
                    var additionalData = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        RepairDescription = completeData.RepairDescription,
                        RepairCost = completeData.RepairCost,
                        PartsUsed = completeData.PartsUsed,
                        TestResult = completeData.TestResult,
                        RequiresApproval = completeData.RequiresApproval
                    });

                    await AddWorkflowHistoryAsync(repairOrderId, technicianId, "完成维修工作",
                        completeData.CompletionComment, repairOrder.Status, 3, additionalData);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成维修工作失败: {repairOrderId}", repairOrderId);
                return (false, $"完成失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> RequestSupportAsync(int repairOrderId, int technicianId, string supportReason)
        {
            try
            {
                await AddWorkflowHistoryAsync(repairOrderId, technicianId, "请求支援", supportReason);
                return (true, "支援请求已记录");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "请求支援失败: {repairOrderId}", repairOrderId);
                return (false, $"请求失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> EscalateRepairOrderAsync(int repairOrderId, int userId, string escalationReason)
        {
            try
            {
                var repairOrder = await _repairOrderService.GetRepairOrderByIdAsync(repairOrderId);
                if (repairOrder == null)
                {
                    return (false, "报修单不存在");
                }

                // 提高优先级
                var newUrgencyLevel = Math.Max(1, repairOrder.UrgencyLevel - 1);
                repairOrder.UrgencyLevel = newUrgencyLevel;
                repairOrder.UpdatedAt = DateTime.Now;

                var result = await _dbContext.Db.Updateable(repairOrder)
                    .UpdateColumns(ro => new { ro.UrgencyLevel, ro.UpdatedAt })
                    .ExecuteCommandAsync();

                if (result > 0)
                {
                    await AddWorkflowHistoryAsync(repairOrderId, userId, "升级报修单", escalationReason);
                    return (true, "报修单已升级");
                }
                else
                {
                    return (false, "升级失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "升级报修单失败: {repairOrderId}", repairOrderId);
                return (false, $"升级失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> SubmitCompletionApprovalAsync(int repairOrderId, int technicianId)
        {
            try
            {
                var result = await ExecuteStatusTransitionAsync(repairOrderId, RepairOrderStatus.PendingConfirmation, technicianId);
                if (result.IsSuccess)
                {
                    await AddWorkflowHistoryAsync(repairOrderId, technicianId, "提交完成审批");
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提交完成审批失败: {repairOrderId}", repairOrderId);
                return (false, $"提交失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> ApproveCompletionAsync(int repairOrderId, int managerId, bool approved, string? comment = null)
        {
            try
            {
                var newStatus = approved ? RepairOrderStatus.Completed : RepairOrderStatus.InProgress;
                var action = approved ? "审批通过" : "审批拒绝";

                var result = await ExecuteStatusTransitionAsync(repairOrderId, newStatus, managerId, comment);
                if (result.IsSuccess)
                {
                    await AddWorkflowHistoryAsync(repairOrderId, managerId, action, comment);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "审批完成失败: {repairOrderId}", repairOrderId);
                return (false, $"审批失败: {ex.Message}");
            }
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> CloseRepairOrderAsync(int repairOrderId, int userId, string? closeComment = null)
        {
            try
            {
                var result = await ExecuteStatusTransitionAsync(repairOrderId, RepairOrderStatus.Closed, userId, closeComment);
                if (result.IsSuccess)
                {
                    await AddWorkflowHistoryAsync(repairOrderId, userId, "关闭报修单", closeComment);
                }
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "关闭报修单失败: {repairOrderId}", repairOrderId);
                return (false, $"关闭失败: {ex.Message}");
            }
        }

        public async Task<List<WorkflowHistoryDto>> GetWorkflowHistoryAsync(int repairOrderId)
        {
            try
            {
                // 使用视图查询工作流历史记录
                var historyRecords = await _dbContext.Db.Queryable<RepairWorkflowHistoryDto>()
                    .AS("V_RepairWorkflowHistoryDetails")
                    .Where(h => h.RepairOrderId == repairOrderId)
                    .OrderByDescending(h => h.CreatedAt)
                    .ToListAsync();

                _logger.LogInformation("获取报修单 {repairOrderId} 的工作流历史记录 {count} 条", repairOrderId, historyRecords.Count);

                // 转换为WorkflowHistoryDto
                return historyRecords.Select(h => new WorkflowHistoryDto
                {
                    Id = h.Id,
                    RepairOrderId = h.RepairOrderId,
                    UserId = h.UserId,
                    UserName = h.UserName,
                    Action = h.Action,
                    Comment = h.Comment,
                    FromStatus = h.FromStatus,
                    ToStatus = h.ToStatus,
                    FromStatusName = h.FromStatusName,
                    ToStatusName = h.ToStatusName,
                    AdditionalData = h.AdditionalData,
                    CreatedAt = h.CreatedAt
                    // 计算属性会自动计算，不需要手动赋值
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作流历史失败: {repairOrderId}", repairOrderId);
                return new List<WorkflowHistoryDto>();
            }
        }

        public async Task<bool> AddWorkflowHistoryAsync(int repairOrderId, int userId, string action, string? comment = null)
        {
            return await AddWorkflowHistoryAsync(repairOrderId, userId, action, comment, null, null, null);
        }

        public async Task<bool> AddWorkflowHistoryAsync(int repairOrderId, int userId, string action, string? comment, int? fromStatus, int? toStatus, string? additionalData = null)
        {
            try
            {
                var history = new RepairWorkflowHistory
                {
                    RepairOrderId = repairOrderId,
                    UserId = userId,
                    Action = action,
                    Comment = comment,
                    FromStatus = fromStatus,
                    ToStatus = toStatus,
                    AdditionalData = additionalData,
                    CreatedAt = DateTime.Now
                };

                var result = await _dbContext.Db.Insertable(history).ExecuteReturnIdentityAsync();

                _logger.LogInformation("添加工作流历史记录成功: 报修单 {repairOrderId}, 用户 {userId}, 操作 {action}, 状态变更 {fromStatus}->{toStatus}, ID {historyId}",
                    repairOrderId, userId, action, fromStatus, toStatus, result);

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加工作流历史失败: {repairOrderId}", repairOrderId);
                return false;
            }
        }
    }
}
