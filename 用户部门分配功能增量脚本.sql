-- 用户部门分配功能增量脚本
-- 在现有数据库基础上添加新的权限和菜单项

-- 1. 添加新权限：用户部门分配
IF NOT EXISTS (SELECT 1 FROM Permissions WHERE Code = 'UserManagement.AssignDepartment')
BEGIN
    INSERT INTO [dbo].[Permissions] ([Code], [Name], [Description], [Module], [Action], [Level], [SortOrder], [IsEnabled], [RouteUrl])
    VALUES ('UserManagement.AssignDepartment', '用户部门分配', '为用户分配部门', 'UserManagement', 'AssignDepartment', 3, 12, 1, NULL);
    
    PRINT '✅ 已添加权限：UserManagement.AssignDepartment';
END
ELSE
BEGIN
    PRINT '⚠️ 权限 UserManagement.AssignDepartment 已存在，跳过';
END

-- 2. 添加新菜单项：用户部门分配管理
DECLARE @SystemManagementId INT;
SELECT @SystemManagementId = Id FROM MenuItems WHERE Code = 'SystemManagement';

IF @SystemManagementId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM MenuItems WHERE Code = 'UserDepartmentAssignmentManagement')
BEGIN
    INSERT INTO [dbo].[MenuItems] ([Code], [Name], [Description], [RouteUrl], [Icon], [ParentId], [Level], [SortOrder], [PermissionCode], [IsEnabled], [IsPublic], [IsSystem], [MenuType])
    VALUES ('UserDepartmentAssignmentManagement', '用户部门分配', '用户部门分配管理页面', 'user-department-assignment-management', 'Icons.Material.Filled.PersonPin', @SystemManagementId, 2, 105, 'UserManagement.AssignDepartment', 1, 0, 1, 1);
    
    PRINT '✅ 已添加菜单项：用户部门分配管理';
END
ELSE
BEGIN
    IF @SystemManagementId IS NULL
        PRINT '❌ 错误：找不到系统管理菜单分组，请检查菜单数据';
    ELSE
        PRINT '⚠️ 菜单项 UserDepartmentAssignmentManagement 已存在，跳过';
END

-- 3. 为管理员角色分配新权限（如果管理员角色存在）
DECLARE @AdminRoleId INT;
SELECT @AdminRoleId = Id FROM Roles WHERE Code = 'Administrator' OR Name = '系统管理员';

DECLARE @AssignDepartmentPermissionId INT;
SELECT @AssignDepartmentPermissionId = Id FROM Permissions WHERE Code = 'UserManagement.AssignDepartment';

IF @AdminRoleId IS NOT NULL AND @AssignDepartmentPermissionId IS NOT NULL
BEGIN
    IF NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = @AdminRoleId AND PermissionId = @AssignDepartmentPermissionId)
    BEGIN
        INSERT INTO [dbo].[RolePermissions] ([RoleId], [PermissionId], [IsEnabled], [CreatedAt])
        VALUES (@AdminRoleId, @AssignDepartmentPermissionId, 1, GETDATE());
        
        PRINT '✅ 已为管理员角色分配用户部门分配权限';
    END
    ELSE
    BEGIN
        PRINT '⚠️ 管理员角色已拥有用户部门分配权限，跳过';
    END
END
ELSE
BEGIN
    IF @AdminRoleId IS NULL
        PRINT '⚠️ 警告：找不到管理员角色，请手动分配权限';
    IF @AssignDepartmentPermissionId IS NULL
        PRINT '❌ 错误：找不到用户部门分配权限，请检查权限创建';
END

-- 4. 验证脚本执行结果
PRINT '';
PRINT '=== 执行结果验证 ===';

-- 检查权限是否创建成功
IF EXISTS (SELECT 1 FROM Permissions WHERE Code = 'UserManagement.AssignDepartment')
    PRINT '✅ 权限验证通过：UserManagement.AssignDepartment';
ELSE
    PRINT '❌ 权限验证失败：UserManagement.AssignDepartment';

-- 检查菜单项是否创建成功
IF EXISTS (SELECT 1 FROM MenuItems WHERE Code = 'UserDepartmentAssignmentManagement')
    PRINT '✅ 菜单验证通过：UserDepartmentAssignmentManagement';
ELSE
    PRINT '❌ 菜单验证失败：UserDepartmentAssignmentManagement';

-- 检查角色权限分配
IF EXISTS (
    SELECT 1 FROM RolePermissions rp
    INNER JOIN Roles r ON rp.RoleId = r.Id
    INNER JOIN Permissions p ON rp.PermissionId = p.Id
    WHERE r.Code = 'Administrator' AND p.Code = 'UserManagement.AssignDepartment'
)
    PRINT '✅ 角色权限验证通过：管理员已拥有用户部门分配权限';
ELSE
    PRINT '⚠️ 角色权限验证：管理员可能未拥有用户部门分配权限，请手动检查';

PRINT '';
PRINT '=== 脚本执行完成 ===';
PRINT '如果所有验证都通过，用户部门分配功能已成功添加到系统中。';
PRINT '请重新启动应用程序以加载新的菜单项。';
